[2025-08-07 18:31:16][sql] CONNECT:[ UseTime:0.019077s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:31:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000735s ]
[2025-08-07 18:31:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000434s ]
[2025-08-07 18:31:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (<PERSON><PERSON><PERSON>, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754562676 [ RunTime:0.011152s ]
[2025-08-07 18:31:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000531s ]
[2025-08-07 18:31:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000246s ]
[2025-08-07 18:31:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000373s ]
[2025-08-07 18:31:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000746s ]
[2025-08-07 18:31:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000294s ]
[2025-08-07 18:31:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000530s ]
[2025-08-07 18:31:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000320s ]
[2025-08-07 18:31:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001750s ]
[2025-08-07 18:32:16][sql] CONNECT:[ UseTime:0.017446s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:32:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000741s ]
[2025-08-07 18:32:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000488s ]
[2025-08-07 18:32:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754562736 [ RunTime:0.009951s ]
[2025-08-07 18:32:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000523s ]
[2025-08-07 18:32:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000307s ]
[2025-08-07 18:32:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000347s ]
[2025-08-07 18:32:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000545s ]
[2025-08-07 18:32:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000230s ]
[2025-08-07 18:32:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000343s ]
[2025-08-07 18:32:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000318s ]
[2025-08-07 18:32:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001715s ]
[2025-08-07 18:32:16][sql] CONNECT:[ UseTime:0.020775s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:32:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000972s ]
[2025-08-07 18:32:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000407s ]
[2025-08-07 18:32:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754562736 [ RunTime:0.000540s ]
[2025-08-07 18:32:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000539s ]
[2025-08-07 18:32:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000253s ]
[2025-08-07 18:32:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000332s ]
[2025-08-07 18:32:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000632s ]
[2025-08-07 18:32:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000231s ]
[2025-08-07 18:32:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000343s ]
[2025-08-07 18:32:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000313s ]
[2025-08-07 18:32:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001745s ]
[2025-08-07 18:33:16][sql] CONNECT:[ UseTime:0.019995s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:33:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000747s ]
[2025-08-07 18:33:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000507s ]
[2025-08-07 18:33:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754562796 [ RunTime:0.012112s ]
[2025-08-07 18:33:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000497s ]
[2025-08-07 18:33:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000253s ]
[2025-08-07 18:33:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000335s ]
[2025-08-07 18:33:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000590s ]
[2025-08-07 18:33:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000268s ]
[2025-08-07 18:33:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000344s ]
[2025-08-07 18:33:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000323s ]
[2025-08-07 18:33:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001818s ]
[2025-08-07 18:33:16][sql] CONNECT:[ UseTime:0.017384s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:33:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000785s ]
[2025-08-07 18:33:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000566s ]
[2025-08-07 18:33:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754562796 [ RunTime:0.009094s ]
[2025-08-07 18:33:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000760s ]
[2025-08-07 18:33:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000340s ]
[2025-08-07 18:33:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000457s ]
[2025-08-07 18:33:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000836s ]
[2025-08-07 18:33:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000303s ]
[2025-08-07 18:33:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000451s ]
[2025-08-07 18:33:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000431s ]
[2025-08-07 18:33:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002948s ]
[2025-08-07 18:34:16][sql] CONNECT:[ UseTime:0.018502s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:34:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000693s ]
[2025-08-07 18:34:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000405s ]
[2025-08-07 18:34:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754562856 [ RunTime:0.016959s ]
[2025-08-07 18:34:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000504s ]
[2025-08-07 18:34:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000248s ]
[2025-08-07 18:34:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000331s ]
[2025-08-07 18:34:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000761s ]
[2025-08-07 18:34:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000239s ]
[2025-08-07 18:34:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000361s ]
[2025-08-07 18:34:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000480s ]
[2025-08-07 18:34:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002229s ]
[2025-08-07 18:34:16][sql] CONNECT:[ UseTime:0.019090s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:34:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000735s ]
[2025-08-07 18:34:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000404s ]
[2025-08-07 18:34:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754562856 [ RunTime:0.044295s ]
[2025-08-07 18:34:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000526s ]
[2025-08-07 18:34:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000249s ]
[2025-08-07 18:34:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000353s ]
[2025-08-07 18:34:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000628s ]
[2025-08-07 18:34:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000228s ]
[2025-08-07 18:34:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000344s ]
[2025-08-07 18:34:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000442s ]
[2025-08-07 18:34:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001711s ]
[2025-08-07 18:35:16][sql] CONNECT:[ UseTime:0.019244s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:35:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000748s ]
[2025-08-07 18:35:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000396s ]
[2025-08-07 18:35:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754562916 [ RunTime:0.009278s ]
[2025-08-07 18:35:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000560s ]
[2025-08-07 18:35:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000253s ]
[2025-08-07 18:35:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000342s ]
[2025-08-07 18:35:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000586s ]
[2025-08-07 18:35:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000235s ]
[2025-08-07 18:35:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000346s ]
[2025-08-07 18:35:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000442s ]
[2025-08-07 18:35:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002402s ]
[2025-08-07 18:35:16][sql] CONNECT:[ UseTime:0.019773s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:35:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000774s ]
[2025-08-07 18:35:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000401s ]
[2025-08-07 18:35:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754562916 [ RunTime:0.012371s ]
[2025-08-07 18:35:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000630s ]
[2025-08-07 18:35:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000262s ]
[2025-08-07 18:35:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000394s ]
[2025-08-07 18:35:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000569s ]
[2025-08-07 18:35:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000218s ]
[2025-08-07 18:35:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000462s ]
[2025-08-07 18:35:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000343s ]
[2025-08-07 18:35:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001731s ]
[2025-08-07 18:36:16][sql] CONNECT:[ UseTime:0.019158s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:36:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000679s ]
[2025-08-07 18:36:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000399s ]
[2025-08-07 18:36:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754562976 [ RunTime:0.012519s ]
[2025-08-07 18:36:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000619s ]
[2025-08-07 18:36:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000339s ]
[2025-08-07 18:36:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000474s ]
[2025-08-07 18:36:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000617s ]
[2025-08-07 18:36:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000291s ]
[2025-08-07 18:36:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000483s ]
[2025-08-07 18:36:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000439s ]
[2025-08-07 18:36:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002113s ]
[2025-08-07 18:36:16][sql] CONNECT:[ UseTime:0.019129s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:36:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000775s ]
[2025-08-07 18:36:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000503s ]
[2025-08-07 18:36:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754562976 [ RunTime:0.009563s ]
[2025-08-07 18:36:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000721s ]
[2025-08-07 18:36:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000350s ]
[2025-08-07 18:36:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000509s ]
[2025-08-07 18:36:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000755s ]
[2025-08-07 18:36:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000351s ]
[2025-08-07 18:36:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000534s ]
[2025-08-07 18:36:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000533s ]
[2025-08-07 18:36:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002141s ]
[2025-08-07 18:37:16][sql] CONNECT:[ UseTime:0.020847s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:37:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000762s ]
[2025-08-07 18:37:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000564s ]
[2025-08-07 18:37:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563036 [ RunTime:0.018628s ]
[2025-08-07 18:37:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000536s ]
[2025-08-07 18:37:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000239s ]
[2025-08-07 18:37:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000327s ]
[2025-08-07 18:37:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000536s ]
[2025-08-07 18:37:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000494s ]
[2025-08-07 18:37:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000635s ]
[2025-08-07 18:37:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000528s ]
[2025-08-07 18:37:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002979s ]
[2025-08-07 18:37:16][sql] CONNECT:[ UseTime:0.019487s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:37:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000944s ]
[2025-08-07 18:37:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000448s ]
[2025-08-07 18:37:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563036 [ RunTime:0.008217s ]
[2025-08-07 18:37:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000523s ]
[2025-08-07 18:37:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000248s ]
[2025-08-07 18:37:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000337s ]
[2025-08-07 18:37:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000569s ]
[2025-08-07 18:37:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000228s ]
[2025-08-07 18:37:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000673s ]
[2025-08-07 18:37:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000566s ]
[2025-08-07 18:37:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002435s ]
[2025-08-07 18:38:16][sql] CONNECT:[ UseTime:0.021064s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:38:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000742s ]
[2025-08-07 18:38:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000422s ]
[2025-08-07 18:38:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563096 [ RunTime:0.014562s ]
[2025-08-07 18:38:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000620s ]
[2025-08-07 18:38:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000504s ]
[2025-08-07 18:38:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000458s ]
[2025-08-07 18:38:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000986s ]
[2025-08-07 18:38:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000256s ]
[2025-08-07 18:38:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000483s ]
[2025-08-07 18:38:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000393s ]
[2025-08-07 18:38:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001815s ]
[2025-08-07 18:38:16][sql] CONNECT:[ UseTime:0.018771s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:38:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000869s ]
[2025-08-07 18:38:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000508s ]
[2025-08-07 18:38:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563096 [ RunTime:0.012922s ]
[2025-08-07 18:38:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000792s ]
[2025-08-07 18:38:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000266s ]
[2025-08-07 18:38:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000350s ]
[2025-08-07 18:38:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000597s ]
[2025-08-07 18:38:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000484s ]
[2025-08-07 18:38:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000368s ]
[2025-08-07 18:38:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000333s ]
[2025-08-07 18:38:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001723s ]
[2025-08-07 18:39:16][sql] CONNECT:[ UseTime:0.020247s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:39:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000771s ]
[2025-08-07 18:39:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000475s ]
[2025-08-07 18:39:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563156 [ RunTime:0.017956s ]
[2025-08-07 18:39:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000796s ]
[2025-08-07 18:39:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000424s ]
[2025-08-07 18:39:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000574s ]
[2025-08-07 18:39:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000677s ]
[2025-08-07 18:39:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000285s ]
[2025-08-07 18:39:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000494s ]
[2025-08-07 18:39:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000443s ]
[2025-08-07 18:39:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001834s ]
[2025-08-07 18:39:16][sql] CONNECT:[ UseTime:0.018986s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:39:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000735s ]
[2025-08-07 18:39:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000509s ]
[2025-08-07 18:39:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563156 [ RunTime:0.009103s ]
[2025-08-07 18:39:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000526s ]
[2025-08-07 18:39:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000252s ]
[2025-08-07 18:39:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000475s ]
[2025-08-07 18:39:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000939s ]
[2025-08-07 18:39:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000396s ]
[2025-08-07 18:39:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000482s ]
[2025-08-07 18:39:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000384s ]
[2025-08-07 18:39:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002973s ]
[2025-08-07 18:40:16][sql] CONNECT:[ UseTime:0.015691s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:40:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.001025s ]
[2025-08-07 18:40:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000502s ]
[2025-08-07 18:40:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563216 [ RunTime:0.015898s ]
[2025-08-07 18:40:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000720s ]
[2025-08-07 18:40:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000314s ]
[2025-08-07 18:40:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000641s ]
[2025-08-07 18:40:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000826s ]
[2025-08-07 18:40:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000324s ]
[2025-08-07 18:40:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000511s ]
[2025-08-07 18:40:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000376s ]
[2025-08-07 18:40:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002293s ]
[2025-08-07 18:40:16][sql] CONNECT:[ UseTime:0.012974s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:40:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000705s ]
[2025-08-07 18:40:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000414s ]
[2025-08-07 18:40:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563216 [ RunTime:0.011077s ]
[2025-08-07 18:40:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000667s ]
[2025-08-07 18:40:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000394s ]
[2025-08-07 18:40:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000431s ]
[2025-08-07 18:40:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000631s ]
[2025-08-07 18:40:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000292s ]
[2025-08-07 18:40:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000460s ]
[2025-08-07 18:40:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000468s ]
[2025-08-07 18:40:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001776s ]
[2025-08-07 18:41:16][sql] CONNECT:[ UseTime:0.019870s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:41:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000722s ]
[2025-08-07 18:41:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000401s ]
[2025-08-07 18:41:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563276 [ RunTime:0.011425s ]
[2025-08-07 18:41:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000522s ]
[2025-08-07 18:41:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000247s ]
[2025-08-07 18:41:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000335s ]
[2025-08-07 18:41:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000789s ]
[2025-08-07 18:41:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000235s ]
[2025-08-07 18:41:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000344s ]
[2025-08-07 18:41:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000319s ]
[2025-08-07 18:41:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002188s ]
[2025-08-07 18:41:16][sql] CONNECT:[ UseTime:0.017590s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:41:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000697s ]
[2025-08-07 18:41:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000436s ]
[2025-08-07 18:41:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563276 [ RunTime:0.000498s ]
[2025-08-07 18:41:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000545s ]
[2025-08-07 18:41:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000247s ]
[2025-08-07 18:41:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000336s ]
[2025-08-07 18:41:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000642s ]
[2025-08-07 18:41:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000232s ]
[2025-08-07 18:41:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000341s ]
[2025-08-07 18:41:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000401s ]
[2025-08-07 18:41:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001802s ]
[2025-08-07 18:42:16][sql] CONNECT:[ UseTime:0.021148s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:42:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000723s ]
[2025-08-07 18:42:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000448s ]
[2025-08-07 18:42:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563336 [ RunTime:0.012232s ]
[2025-08-07 18:42:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000514s ]
[2025-08-07 18:42:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000236s ]
[2025-08-07 18:42:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000326s ]
[2025-08-07 18:42:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000539s ]
[2025-08-07 18:42:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000225s ]
[2025-08-07 18:42:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000339s ]
[2025-08-07 18:42:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000340s ]
[2025-08-07 18:42:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001960s ]
[2025-08-07 18:42:16][sql] CONNECT:[ UseTime:0.020512s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:42:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000729s ]
[2025-08-07 18:42:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000404s ]
[2025-08-07 18:42:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563336 [ RunTime:0.005274s ]
[2025-08-07 18:42:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000742s ]
[2025-08-07 18:42:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000255s ]
[2025-08-07 18:42:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000330s ]
[2025-08-07 18:42:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000581s ]
[2025-08-07 18:42:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000228s ]
[2025-08-07 18:42:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000344s ]
[2025-08-07 18:42:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000435s ]
[2025-08-07 18:42:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001823s ]
[2025-08-07 18:43:16][sql] CONNECT:[ UseTime:0.019941s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:43:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000869s ]
[2025-08-07 18:43:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000399s ]
[2025-08-07 18:43:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563396 [ RunTime:0.013849s ]
[2025-08-07 18:43:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000541s ]
[2025-08-07 18:43:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000329s ]
[2025-08-07 18:43:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000336s ]
[2025-08-07 18:43:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000612s ]
[2025-08-07 18:43:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000229s ]
[2025-08-07 18:43:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000340s ]
[2025-08-07 18:43:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000379s ]
[2025-08-07 18:43:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002571s ]
[2025-08-07 18:43:16][sql] CONNECT:[ UseTime:0.021070s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:43:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000735s ]
[2025-08-07 18:43:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000400s ]
[2025-08-07 18:43:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563396 [ RunTime:0.000638s ]
[2025-08-07 18:43:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000526s ]
[2025-08-07 18:43:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000246s ]
[2025-08-07 18:43:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000331s ]
[2025-08-07 18:43:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000580s ]
[2025-08-07 18:43:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000238s ]
[2025-08-07 18:43:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000340s ]
[2025-08-07 18:43:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000308s ]
[2025-08-07 18:43:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001760s ]
[2025-08-07 18:44:16][sql] CONNECT:[ UseTime:0.020798s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:44:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000864s ]
[2025-08-07 18:44:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000389s ]
[2025-08-07 18:44:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563456 [ RunTime:0.007089s ]
[2025-08-07 18:44:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000519s ]
[2025-08-07 18:44:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000250s ]
[2025-08-07 18:44:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000335s ]
[2025-08-07 18:44:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000581s ]
[2025-08-07 18:44:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000231s ]
[2025-08-07 18:44:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000346s ]
[2025-08-07 18:44:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000318s ]
[2025-08-07 18:44:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001769s ]
[2025-08-07 18:44:16][sql] CONNECT:[ UseTime:0.020668s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:44:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000736s ]
[2025-08-07 18:44:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000430s ]
[2025-08-07 18:44:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563456 [ RunTime:0.006918s ]
[2025-08-07 18:44:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000511s ]
[2025-08-07 18:44:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000252s ]
[2025-08-07 18:44:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000336s ]
[2025-08-07 18:44:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000576s ]
[2025-08-07 18:44:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000232s ]
[2025-08-07 18:44:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000343s ]
[2025-08-07 18:44:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000419s ]
[2025-08-07 18:44:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001948s ]
[2025-08-07 18:45:16][sql] CONNECT:[ UseTime:0.020370s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:45:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000671s ]
[2025-08-07 18:45:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000391s ]
[2025-08-07 18:45:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563516 [ RunTime:0.017311s ]
[2025-08-07 18:45:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000668s ]
[2025-08-07 18:45:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000394s ]
[2025-08-07 18:45:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000536s ]
[2025-08-07 18:45:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000858s ]
[2025-08-07 18:45:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000395s ]
[2025-08-07 18:45:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000517s ]
[2025-08-07 18:45:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000450s ]
[2025-08-07 18:45:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001826s ]
[2025-08-07 18:45:16][sql] CONNECT:[ UseTime:0.013596s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:45:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000775s ]
[2025-08-07 18:45:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000427s ]
[2025-08-07 18:45:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563516 [ RunTime:0.000600s ]
[2025-08-07 18:45:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000525s ]
[2025-08-07 18:45:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000244s ]
[2025-08-07 18:45:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000338s ]
[2025-08-07 18:45:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000640s ]
[2025-08-07 18:45:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000231s ]
[2025-08-07 18:45:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000522s ]
[2025-08-07 18:45:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000338s ]
[2025-08-07 18:45:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001733s ]
[2025-08-07 18:46:16][sql] CONNECT:[ UseTime:0.020406s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:46:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000740s ]
[2025-08-07 18:46:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000452s ]
[2025-08-07 18:46:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563576 [ RunTime:0.007646s ]
[2025-08-07 18:46:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000622s ]
[2025-08-07 18:46:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000280s ]
[2025-08-07 18:46:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000342s ]
[2025-08-07 18:46:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000590s ]
[2025-08-07 18:46:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000228s ]
[2025-08-07 18:46:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000356s ]
[2025-08-07 18:46:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000340s ]
[2025-08-07 18:46:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001727s ]
[2025-08-07 18:46:16][sql] CONNECT:[ UseTime:0.020944s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:46:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000770s ]
[2025-08-07 18:46:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000401s ]
[2025-08-07 18:46:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563576 [ RunTime:0.006082s ]
[2025-08-07 18:46:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000527s ]
[2025-08-07 18:46:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000237s ]
[2025-08-07 18:46:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000327s ]
[2025-08-07 18:46:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000531s ]
[2025-08-07 18:46:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000224s ]
[2025-08-07 18:46:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000341s ]
[2025-08-07 18:46:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000313s ]
[2025-08-07 18:46:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002617s ]
[2025-08-07 18:47:16][sql] CONNECT:[ UseTime:0.020562s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:47:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000918s ]
[2025-08-07 18:47:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000407s ]
[2025-08-07 18:47:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563636 [ RunTime:0.013133s ]
[2025-08-07 18:47:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000618s ]
[2025-08-07 18:47:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000273s ]
[2025-08-07 18:47:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000339s ]
[2025-08-07 18:47:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000711s ]
[2025-08-07 18:47:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000532s ]
[2025-08-07 18:47:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000586s ]
[2025-08-07 18:47:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000483s ]
[2025-08-07 18:47:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002303s ]
[2025-08-07 18:47:16][sql] CONNECT:[ UseTime:0.019657s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:47:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000841s ]
[2025-08-07 18:47:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000401s ]
[2025-08-07 18:47:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563636 [ RunTime:0.007503s ]
[2025-08-07 18:47:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000531s ]
[2025-08-07 18:47:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000244s ]
[2025-08-07 18:47:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000334s ]
[2025-08-07 18:47:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000539s ]
[2025-08-07 18:47:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000230s ]
[2025-08-07 18:47:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000469s ]
[2025-08-07 18:47:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000330s ]
[2025-08-07 18:47:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001913s ]
[2025-08-07 18:48:16][sql] CONNECT:[ UseTime:0.020598s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:48:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000903s ]
[2025-08-07 18:48:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000448s ]
[2025-08-07 18:48:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563696 [ RunTime:0.011128s ]
[2025-08-07 18:48:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000537s ]
[2025-08-07 18:48:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000249s ]
[2025-08-07 18:48:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000337s ]
[2025-08-07 18:48:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000573s ]
[2025-08-07 18:48:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000230s ]
[2025-08-07 18:48:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000343s ]
[2025-08-07 18:48:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000314s ]
[2025-08-07 18:48:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001857s ]
[2025-08-07 18:48:16][sql] CONNECT:[ UseTime:0.020454s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:48:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.001153s ]
[2025-08-07 18:48:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000431s ]
[2025-08-07 18:48:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563696 [ RunTime:0.008329s ]
[2025-08-07 18:48:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000623s ]
[2025-08-07 18:48:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000325s ]
[2025-08-07 18:48:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000356s ]
[2025-08-07 18:48:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000587s ]
[2025-08-07 18:48:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000231s ]
[2025-08-07 18:48:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000370s ]
[2025-08-07 18:48:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000314s ]
[2025-08-07 18:48:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001774s ]
[2025-08-07 18:49:16][sql] CONNECT:[ UseTime:0.021808s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:49:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000823s ]
[2025-08-07 18:49:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000402s ]
[2025-08-07 18:49:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563756 [ RunTime:0.044745s ]
[2025-08-07 18:49:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000707s ]
[2025-08-07 18:49:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000351s ]
[2025-08-07 18:49:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000471s ]
[2025-08-07 18:49:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000924s ]
[2025-08-07 18:49:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000336s ]
[2025-08-07 18:49:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000500s ]
[2025-08-07 18:49:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000432s ]
[2025-08-07 18:49:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002951s ]
[2025-08-07 18:49:16][sql] CONNECT:[ UseTime:0.016421s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:49:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000733s ]
[2025-08-07 18:49:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000418s ]
[2025-08-07 18:49:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563756 [ RunTime:0.000224s ]
[2025-08-07 18:49:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000513s ]
[2025-08-07 18:49:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000277s ]
[2025-08-07 18:49:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000366s ]
[2025-08-07 18:49:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000572s ]
[2025-08-07 18:49:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000228s ]
[2025-08-07 18:49:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000341s ]
[2025-08-07 18:49:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000311s ]
[2025-08-07 18:49:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002042s ]
[2025-08-07 18:50:16][sql] CONNECT:[ UseTime:0.019924s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:50:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000754s ]
[2025-08-07 18:50:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000394s ]
[2025-08-07 18:50:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563816 [ RunTime:0.000238s ]
[2025-08-07 18:50:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000550s ]
[2025-08-07 18:50:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000245s ]
[2025-08-07 18:50:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000336s ]
[2025-08-07 18:50:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000540s ]
[2025-08-07 18:50:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000233s ]
[2025-08-07 18:50:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000382s ]
[2025-08-07 18:50:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000348s ]
[2025-08-07 18:50:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001745s ]
[2025-08-07 18:50:16][sql] CONNECT:[ UseTime:0.022023s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:50:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000761s ]
[2025-08-07 18:50:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000394s ]
[2025-08-07 18:50:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563816 [ RunTime:0.000219s ]
[2025-08-07 18:50:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000500s ]
[2025-08-07 18:50:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000242s ]
[2025-08-07 18:50:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000348s ]
[2025-08-07 18:50:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000537s ]
[2025-08-07 18:50:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000319s ]
[2025-08-07 18:50:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000369s ]
[2025-08-07 18:50:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000327s ]
[2025-08-07 18:50:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001708s ]
[2025-08-07 18:51:16][sql] CONNECT:[ UseTime:0.019684s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:51:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000738s ]
[2025-08-07 18:51:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000502s ]
[2025-08-07 18:51:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563876 [ RunTime:0.000220s ]
[2025-08-07 18:51:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000555s ]
[2025-08-07 18:51:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000298s ]
[2025-08-07 18:51:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000336s ]
[2025-08-07 18:51:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000599s ]
[2025-08-07 18:51:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000226s ]
[2025-08-07 18:51:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000341s ]
[2025-08-07 18:51:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000315s ]
[2025-08-07 18:51:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001715s ]
[2025-08-07 18:51:16][sql] CONNECT:[ UseTime:0.021280s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:51:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000798s ]
[2025-08-07 18:51:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000401s ]
[2025-08-07 18:51:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563876 [ RunTime:0.000260s ]
[2025-08-07 18:51:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000514s ]
[2025-08-07 18:51:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000244s ]
[2025-08-07 18:51:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000324s ]
[2025-08-07 18:51:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000713s ]
[2025-08-07 18:51:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000251s ]
[2025-08-07 18:51:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000353s ]
[2025-08-07 18:51:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000352s ]
[2025-08-07 18:51:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001873s ]
[2025-08-07 18:52:16][sql] CONNECT:[ UseTime:0.019458s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:52:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000729s ]
[2025-08-07 18:52:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000425s ]
[2025-08-07 18:52:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563936 [ RunTime:0.000238s ]
[2025-08-07 18:52:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000532s ]
[2025-08-07 18:52:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000251s ]
[2025-08-07 18:52:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000530s ]
[2025-08-07 18:52:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000617s ]
[2025-08-07 18:52:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000230s ]
[2025-08-07 18:52:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000347s ]
[2025-08-07 18:52:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000314s ]
[2025-08-07 18:52:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001653s ]
[2025-08-07 18:52:16][sql] CONNECT:[ UseTime:0.020298s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:52:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000754s ]
[2025-08-07 18:52:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000402s ]
[2025-08-07 18:52:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563936 [ RunTime:0.000229s ]
[2025-08-07 18:52:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000537s ]
[2025-08-07 18:52:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000246s ]
[2025-08-07 18:52:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000331s ]
[2025-08-07 18:52:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000565s ]
[2025-08-07 18:52:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000230s ]
[2025-08-07 18:52:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000338s ]
[2025-08-07 18:52:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000312s ]
[2025-08-07 18:52:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001769s ]
[2025-08-07 18:53:16][sql] CONNECT:[ UseTime:0.020652s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:53:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000844s ]
[2025-08-07 18:53:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000395s ]
[2025-08-07 18:53:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563996 [ RunTime:0.000216s ]
[2025-08-07 18:53:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000530s ]
[2025-08-07 18:53:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000249s ]
[2025-08-07 18:53:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000334s ]
[2025-08-07 18:53:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000578s ]
[2025-08-07 18:53:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000233s ]
[2025-08-07 18:53:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000523s ]
[2025-08-07 18:53:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000324s ]
[2025-08-07 18:53:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001893s ]
[2025-08-07 18:53:16][sql] CONNECT:[ UseTime:0.000664s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:53:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000666s ]
[2025-08-07 18:53:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000400s ]
[2025-08-07 18:53:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754563996 [ RunTime:0.000224s ]
[2025-08-07 18:53:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000822s ]
[2025-08-07 18:53:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000250s ]
[2025-08-07 18:53:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000335s ]
[2025-08-07 18:53:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000691s ]
[2025-08-07 18:53:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000269s ]
[2025-08-07 18:53:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000407s ]
[2025-08-07 18:53:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000320s ]
[2025-08-07 18:53:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001781s ]
[2025-08-07 18:54:16][sql] CONNECT:[ UseTime:0.021248s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:54:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000740s ]
[2025-08-07 18:54:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000454s ]
[2025-08-07 18:54:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564056 [ RunTime:0.000221s ]
[2025-08-07 18:54:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000542s ]
[2025-08-07 18:54:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000267s ]
[2025-08-07 18:54:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000323s ]
[2025-08-07 18:54:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000599s ]
[2025-08-07 18:54:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000230s ]
[2025-08-07 18:54:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000458s ]
[2025-08-07 18:54:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000318s ]
[2025-08-07 18:54:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001795s ]
[2025-08-07 18:54:16][sql] CONNECT:[ UseTime:0.021570s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:54:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000732s ]
[2025-08-07 18:54:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000393s ]
[2025-08-07 18:54:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564056 [ RunTime:0.000218s ]
[2025-08-07 18:54:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000493s ]
[2025-08-07 18:54:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000255s ]
[2025-08-07 18:54:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000337s ]
[2025-08-07 18:54:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000566s ]
[2025-08-07 18:54:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000227s ]
[2025-08-07 18:54:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000565s ]
[2025-08-07 18:54:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000340s ]
[2025-08-07 18:54:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001731s ]
[2025-08-07 18:55:16][sql] CONNECT:[ UseTime:0.020207s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:55:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000753s ]
[2025-08-07 18:55:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000405s ]
[2025-08-07 18:55:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564116 [ RunTime:0.000223s ]
[2025-08-07 18:55:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000529s ]
[2025-08-07 18:55:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000248s ]
[2025-08-07 18:55:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000382s ]
[2025-08-07 18:55:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000607s ]
[2025-08-07 18:55:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000248s ]
[2025-08-07 18:55:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000359s ]
[2025-08-07 18:55:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000316s ]
[2025-08-07 18:55:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001700s ]
[2025-08-07 18:55:16][sql] CONNECT:[ UseTime:0.021356s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:55:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000688s ]
[2025-08-07 18:55:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000408s ]
[2025-08-07 18:55:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564116 [ RunTime:0.000228s ]
[2025-08-07 18:55:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000553s ]
[2025-08-07 18:55:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000246s ]
[2025-08-07 18:55:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000325s ]
[2025-08-07 18:55:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000629s ]
[2025-08-07 18:55:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000227s ]
[2025-08-07 18:55:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000340s ]
[2025-08-07 18:55:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000311s ]
[2025-08-07 18:55:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001724s ]
[2025-08-07 18:56:16][sql] CONNECT:[ UseTime:0.019217s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:56:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000748s ]
[2025-08-07 18:56:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000450s ]
[2025-08-07 18:56:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564176 [ RunTime:0.000220s ]
[2025-08-07 18:56:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000512s ]
[2025-08-07 18:56:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000249s ]
[2025-08-07 18:56:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000345s ]
[2025-08-07 18:56:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000610s ]
[2025-08-07 18:56:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000235s ]
[2025-08-07 18:56:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000461s ]
[2025-08-07 18:56:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000426s ]
[2025-08-07 18:56:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001754s ]
[2025-08-07 18:56:16][sql] CONNECT:[ UseTime:0.015766s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:56:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000853s ]
[2025-08-07 18:56:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000445s ]
[2025-08-07 18:56:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564176 [ RunTime:0.000228s ]
[2025-08-07 18:56:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000538s ]
[2025-08-07 18:56:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000322s ]
[2025-08-07 18:56:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000359s ]
[2025-08-07 18:56:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000581s ]
[2025-08-07 18:56:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000236s ]
[2025-08-07 18:56:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000340s ]
[2025-08-07 18:56:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000309s ]
[2025-08-07 18:56:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001686s ]
[2025-08-07 18:57:16][sql] CONNECT:[ UseTime:0.020357s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:57:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000691s ]
[2025-08-07 18:57:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000406s ]
[2025-08-07 18:57:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564236 [ RunTime:0.000224s ]
[2025-08-07 18:57:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000566s ]
[2025-08-07 18:57:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000242s ]
[2025-08-07 18:57:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000629s ]
[2025-08-07 18:57:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000753s ]
[2025-08-07 18:57:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000242s ]
[2025-08-07 18:57:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000356s ]
[2025-08-07 18:57:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000324s ]
[2025-08-07 18:57:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001730s ]
[2025-08-07 18:57:16][sql] CONNECT:[ UseTime:0.020407s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:57:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000732s ]
[2025-08-07 18:57:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000412s ]
[2025-08-07 18:57:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564236 [ RunTime:0.000221s ]
[2025-08-07 18:57:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000533s ]
[2025-08-07 18:57:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000252s ]
[2025-08-07 18:57:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000359s ]
[2025-08-07 18:57:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000578s ]
[2025-08-07 18:57:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000236s ]
[2025-08-07 18:57:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000347s ]
[2025-08-07 18:57:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000321s ]
[2025-08-07 18:57:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002060s ]
[2025-08-07 18:58:16][sql] CONNECT:[ UseTime:0.017877s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:58:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000777s ]
[2025-08-07 18:58:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000408s ]
[2025-08-07 18:58:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564296 [ RunTime:0.000225s ]
[2025-08-07 18:58:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000525s ]
[2025-08-07 18:58:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000239s ]
[2025-08-07 18:58:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000322s ]
[2025-08-07 18:58:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000656s ]
[2025-08-07 18:58:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000232s ]
[2025-08-07 18:58:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000347s ]
[2025-08-07 18:58:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000323s ]
[2025-08-07 18:58:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001819s ]
[2025-08-07 18:58:16][sql] CONNECT:[ UseTime:0.020410s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:58:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.001042s ]
[2025-08-07 18:58:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000403s ]
[2025-08-07 18:58:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564296 [ RunTime:0.000220s ]
[2025-08-07 18:58:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000496s ]
[2025-08-07 18:58:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000246s ]
[2025-08-07 18:58:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000334s ]
[2025-08-07 18:58:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000589s ]
[2025-08-07 18:58:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000227s ]
[2025-08-07 18:58:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000454s ]
[2025-08-07 18:58:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000371s ]
[2025-08-07 18:58:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001737s ]
[2025-08-07 18:59:16][sql] CONNECT:[ UseTime:0.020203s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:59:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000732s ]
[2025-08-07 18:59:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000495s ]
[2025-08-07 18:59:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564356 [ RunTime:0.000290s ]
[2025-08-07 18:59:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000500s ]
[2025-08-07 18:59:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000258s ]
[2025-08-07 18:59:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000332s ]
[2025-08-07 18:59:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000585s ]
[2025-08-07 18:59:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000231s ]
[2025-08-07 18:59:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000349s ]
[2025-08-07 18:59:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000315s ]
[2025-08-07 18:59:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001814s ]
[2025-08-07 18:59:16][sql] CONNECT:[ UseTime:0.020694s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 18:59:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000733s ]
[2025-08-07 18:59:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000391s ]
[2025-08-07 18:59:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564356 [ RunTime:0.000213s ]
[2025-08-07 18:59:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000540s ]
[2025-08-07 18:59:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000252s ]
[2025-08-07 18:59:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000339s ]
[2025-08-07 18:59:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000636s ]
[2025-08-07 18:59:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000232s ]
[2025-08-07 18:59:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000342s ]
[2025-08-07 18:59:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000321s ]
[2025-08-07 18:59:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001844s ]
[2025-08-07 19:00:16][sql] CONNECT:[ UseTime:0.021753s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:00:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000764s ]
[2025-08-07 19:00:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000475s ]
[2025-08-07 19:00:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564416 [ RunTime:0.000220s ]
[2025-08-07 19:00:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000538s ]
[2025-08-07 19:00:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000293s ]
[2025-08-07 19:00:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000380s ]
[2025-08-07 19:00:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000581s ]
[2025-08-07 19:00:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000225s ]
[2025-08-07 19:00:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000358s ]
[2025-08-07 19:00:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000327s ]
[2025-08-07 19:00:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002360s ]
[2025-08-07 19:00:16][sql] CONNECT:[ UseTime:0.018447s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:00:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000883s ]
[2025-08-07 19:00:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000397s ]
[2025-08-07 19:00:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564416 [ RunTime:0.000228s ]
[2025-08-07 19:00:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000525s ]
[2025-08-07 19:00:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000249s ]
[2025-08-07 19:00:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000339s ]
[2025-08-07 19:00:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000593s ]
[2025-08-07 19:00:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000289s ]
[2025-08-07 19:00:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000343s ]
[2025-08-07 19:00:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000314s ]
[2025-08-07 19:00:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001696s ]
[2025-08-07 19:01:16][sql] CONNECT:[ UseTime:0.017794s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:01:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000695s ]
[2025-08-07 19:01:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000486s ]
[2025-08-07 19:01:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564476 [ RunTime:0.000298s ]
[2025-08-07 19:01:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000540s ]
[2025-08-07 19:01:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000251s ]
[2025-08-07 19:01:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000334s ]
[2025-08-07 19:01:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000582s ]
[2025-08-07 19:01:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000228s ]
[2025-08-07 19:01:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000343s ]
[2025-08-07 19:01:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000320s ]
[2025-08-07 19:01:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002145s ]
[2025-08-07 19:01:16][sql] CONNECT:[ UseTime:0.020178s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:01:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000739s ]
[2025-08-07 19:01:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000395s ]
[2025-08-07 19:01:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564476 [ RunTime:0.000221s ]
[2025-08-07 19:01:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000619s ]
[2025-08-07 19:01:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000249s ]
[2025-08-07 19:01:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000411s ]
[2025-08-07 19:01:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000532s ]
[2025-08-07 19:01:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000226s ]
[2025-08-07 19:01:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000341s ]
[2025-08-07 19:01:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000321s ]
[2025-08-07 19:01:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001970s ]
[2025-08-07 19:02:16][sql] CONNECT:[ UseTime:0.021528s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:02:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000727s ]
[2025-08-07 19:02:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000394s ]
[2025-08-07 19:02:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564536 [ RunTime:0.000222s ]
[2025-08-07 19:02:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000571s ]
[2025-08-07 19:02:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000286s ]
[2025-08-07 19:02:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000337s ]
[2025-08-07 19:02:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000578s ]
[2025-08-07 19:02:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000250s ]
[2025-08-07 19:02:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000403s ]
[2025-08-07 19:02:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000321s ]
[2025-08-07 19:02:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001708s ]
[2025-08-07 19:02:16][sql] CONNECT:[ UseTime:0.019737s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:02:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000744s ]
[2025-08-07 19:02:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000403s ]
[2025-08-07 19:02:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564536 [ RunTime:0.000227s ]
[2025-08-07 19:02:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000543s ]
[2025-08-07 19:02:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000252s ]
[2025-08-07 19:02:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000331s ]
[2025-08-07 19:02:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000588s ]
[2025-08-07 19:02:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000233s ]
[2025-08-07 19:02:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000346s ]
[2025-08-07 19:02:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000561s ]
[2025-08-07 19:02:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001942s ]
[2025-08-07 19:03:16][sql] CONNECT:[ UseTime:0.017739s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:03:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000712s ]
[2025-08-07 19:03:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000404s ]
[2025-08-07 19:03:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564596 [ RunTime:0.000219s ]
[2025-08-07 19:03:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000531s ]
[2025-08-07 19:03:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000253s ]
[2025-08-07 19:03:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000324s ]
[2025-08-07 19:03:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000580s ]
[2025-08-07 19:03:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000228s ]
[2025-08-07 19:03:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000353s ]
[2025-08-07 19:03:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000307s ]
[2025-08-07 19:03:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001785s ]
[2025-08-07 19:03:16][sql] CONNECT:[ UseTime:0.018993s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:03:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000787s ]
[2025-08-07 19:03:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000391s ]
[2025-08-07 19:03:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564596 [ RunTime:0.000217s ]
[2025-08-07 19:03:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000647s ]
[2025-08-07 19:03:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000256s ]
[2025-08-07 19:03:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000449s ]
[2025-08-07 19:03:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000631s ]
[2025-08-07 19:03:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000244s ]
[2025-08-07 19:03:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000338s ]
[2025-08-07 19:03:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000306s ]
[2025-08-07 19:03:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001721s ]
[2025-08-07 19:04:16][sql] CONNECT:[ UseTime:0.019570s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:04:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000748s ]
[2025-08-07 19:04:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000437s ]
[2025-08-07 19:04:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564656 [ RunTime:0.000221s ]
[2025-08-07 19:04:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000537s ]
[2025-08-07 19:04:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000256s ]
[2025-08-07 19:04:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000335s ]
[2025-08-07 19:04:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000597s ]
[2025-08-07 19:04:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000254s ]
[2025-08-07 19:04:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000348s ]
[2025-08-07 19:04:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000322s ]
[2025-08-07 19:04:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001732s ]
[2025-08-07 19:04:16][sql] CONNECT:[ UseTime:0.019879s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:04:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000726s ]
[2025-08-07 19:04:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000395s ]
[2025-08-07 19:04:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564656 [ RunTime:0.000215s ]
[2025-08-07 19:04:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000539s ]
[2025-08-07 19:04:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000239s ]
[2025-08-07 19:04:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000328s ]
[2025-08-07 19:04:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000536s ]
[2025-08-07 19:04:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000226s ]
[2025-08-07 19:04:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000341s ]
[2025-08-07 19:04:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000307s ]
[2025-08-07 19:04:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001725s ]
[2025-08-07 19:05:16][sql] CONNECT:[ UseTime:0.020391s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:05:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000770s ]
[2025-08-07 19:05:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000389s ]
[2025-08-07 19:05:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564716 [ RunTime:0.000216s ]
[2025-08-07 19:05:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000560s ]
[2025-08-07 19:05:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000343s ]
[2025-08-07 19:05:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000375s ]
[2025-08-07 19:05:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000593s ]
[2025-08-07 19:05:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000240s ]
[2025-08-07 19:05:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000423s ]
[2025-08-07 19:05:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000384s ]
[2025-08-07 19:05:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001731s ]
[2025-08-07 19:05:16][sql] CONNECT:[ UseTime:0.020493s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:05:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000691s ]
[2025-08-07 19:05:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000460s ]
[2025-08-07 19:05:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564716 [ RunTime:0.012728s ]
[2025-08-07 19:05:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000525s ]
[2025-08-07 19:05:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000241s ]
[2025-08-07 19:05:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000508s ]
[2025-08-07 19:05:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000556s ]
[2025-08-07 19:05:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000293s ]
[2025-08-07 19:05:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000356s ]
[2025-08-07 19:05:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000329s ]
[2025-08-07 19:05:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001708s ]
[2025-08-07 19:06:16][sql] CONNECT:[ UseTime:0.020398s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:06:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000732s ]
[2025-08-07 19:06:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000410s ]
[2025-08-07 19:06:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564776 [ RunTime:0.011138s ]
[2025-08-07 19:06:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000497s ]
[2025-08-07 19:06:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000246s ]
[2025-08-07 19:06:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000334s ]
[2025-08-07 19:06:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000583s ]
[2025-08-07 19:06:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000228s ]
[2025-08-07 19:06:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000338s ]
[2025-08-07 19:06:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000314s ]
[2025-08-07 19:06:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001681s ]
[2025-08-07 19:06:16][sql] CONNECT:[ UseTime:0.020974s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:06:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000812s ]
[2025-08-07 19:06:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000409s ]
[2025-08-07 19:06:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564776 [ RunTime:0.012069s ]
[2025-08-07 19:06:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000683s ]
[2025-08-07 19:06:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000315s ]
[2025-08-07 19:06:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000500s ]
[2025-08-07 19:06:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000599s ]
[2025-08-07 19:06:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000358s ]
[2025-08-07 19:06:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000495s ]
[2025-08-07 19:06:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000555s ]
[2025-08-07 19:06:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001958s ]
[2025-08-07 19:07:16][sql] CONNECT:[ UseTime:0.021167s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:07:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000734s ]
[2025-08-07 19:07:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000506s ]
[2025-08-07 19:07:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564836 [ RunTime:0.012060s ]
[2025-08-07 19:07:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000533s ]
[2025-08-07 19:07:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000342s ]
[2025-08-07 19:07:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000332s ]
[2025-08-07 19:07:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000580s ]
[2025-08-07 19:07:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000227s ]
[2025-08-07 19:07:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000344s ]
[2025-08-07 19:07:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000319s ]
[2025-08-07 19:07:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002212s ]
[2025-08-07 19:07:16][sql] CONNECT:[ UseTime:0.020227s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:07:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000797s ]
[2025-08-07 19:07:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000401s ]
[2025-08-07 19:07:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564836 [ RunTime:0.000588s ]
[2025-08-07 19:07:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000539s ]
[2025-08-07 19:07:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000281s ]
[2025-08-07 19:07:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000357s ]
[2025-08-07 19:07:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000578s ]
[2025-08-07 19:07:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000275s ]
[2025-08-07 19:07:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000339s ]
[2025-08-07 19:07:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000307s ]
[2025-08-07 19:07:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001684s ]
[2025-08-07 19:08:16][sql] CONNECT:[ UseTime:0.020333s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:08:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000732s ]
[2025-08-07 19:08:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000389s ]
[2025-08-07 19:08:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564896 [ RunTime:0.008267s ]
[2025-08-07 19:08:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000515s ]
[2025-08-07 19:08:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000240s ]
[2025-08-07 19:08:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000335s ]
[2025-08-07 19:08:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000545s ]
[2025-08-07 19:08:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000226s ]
[2025-08-07 19:08:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000344s ]
[2025-08-07 19:08:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000323s ]
[2025-08-07 19:08:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001781s ]
[2025-08-07 19:08:16][sql] CONNECT:[ UseTime:0.018867s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:08:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000740s ]
[2025-08-07 19:08:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000541s ]
[2025-08-07 19:08:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564896 [ RunTime:0.033250s ]
[2025-08-07 19:08:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000748s ]
[2025-08-07 19:08:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000348s ]
[2025-08-07 19:08:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000435s ]
[2025-08-07 19:08:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000914s ]
[2025-08-07 19:08:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000337s ]
[2025-08-07 19:08:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000487s ]
[2025-08-07 19:08:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000441s ]
[2025-08-07 19:08:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002946s ]
[2025-08-07 19:09:16][sql] CONNECT:[ UseTime:0.021770s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:09:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000735s ]
[2025-08-07 19:09:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000503s ]
[2025-08-07 19:09:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564956 [ RunTime:0.020777s ]
[2025-08-07 19:09:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000802s ]
[2025-08-07 19:09:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000368s ]
[2025-08-07 19:09:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000563s ]
[2025-08-07 19:09:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000791s ]
[2025-08-07 19:09:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000433s ]
[2025-08-07 19:09:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000538s ]
[2025-08-07 19:09:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000448s ]
[2025-08-07 19:09:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001819s ]
[2025-08-07 19:09:16][sql] CONNECT:[ UseTime:0.014761s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:09:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000754s ]
[2025-08-07 19:09:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000583s ]
[2025-08-07 19:09:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754564956 [ RunTime:0.000523s ]
[2025-08-07 19:09:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000533s ]
[2025-08-07 19:09:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000238s ]
[2025-08-07 19:09:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000337s ]
[2025-08-07 19:09:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000682s ]
[2025-08-07 19:09:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000337s ]
[2025-08-07 19:09:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000353s ]
[2025-08-07 19:09:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000319s ]
[2025-08-07 19:09:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001734s ]
[2025-08-07 19:10:16][sql] CONNECT:[ UseTime:0.019868s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:10:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000755s ]
[2025-08-07 19:10:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000404s ]
[2025-08-07 19:10:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565016 [ RunTime:0.016995s ]
[2025-08-07 19:10:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000615s ]
[2025-08-07 19:10:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000277s ]
[2025-08-07 19:10:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000345s ]
[2025-08-07 19:10:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000839s ]
[2025-08-07 19:10:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000458s ]
[2025-08-07 19:10:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000628s ]
[2025-08-07 19:10:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000477s ]
[2025-08-07 19:10:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002238s ]
[2025-08-07 19:10:16][sql] CONNECT:[ UseTime:0.020259s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:10:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000876s ]
[2025-08-07 19:10:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000400s ]
[2025-08-07 19:10:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565016 [ RunTime:0.000527s ]
[2025-08-07 19:10:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000529s ]
[2025-08-07 19:10:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000245s ]
[2025-08-07 19:10:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000381s ]
[2025-08-07 19:10:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000580s ]
[2025-08-07 19:10:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000226s ]
[2025-08-07 19:10:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000344s ]
[2025-08-07 19:10:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000318s ]
[2025-08-07 19:10:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002044s ]
[2025-08-07 19:11:16][sql] CONNECT:[ UseTime:0.021231s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:11:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000705s ]
[2025-08-07 19:11:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000479s ]
[2025-08-07 19:11:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565076 [ RunTime:0.004805s ]
[2025-08-07 19:11:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000538s ]
[2025-08-07 19:11:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000249s ]
[2025-08-07 19:11:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000331s ]
[2025-08-07 19:11:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000581s ]
[2025-08-07 19:11:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000229s ]
[2025-08-07 19:11:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000347s ]
[2025-08-07 19:11:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000327s ]
[2025-08-07 19:11:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001726s ]
[2025-08-07 19:11:16][sql] CONNECT:[ UseTime:0.019425s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:11:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000729s ]
[2025-08-07 19:11:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000399s ]
[2025-08-07 19:11:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565076 [ RunTime:0.000593s ]
[2025-08-07 19:11:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000543s ]
[2025-08-07 19:11:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000258s ]
[2025-08-07 19:11:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000327s ]
[2025-08-07 19:11:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000673s ]
[2025-08-07 19:11:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000250s ]
[2025-08-07 19:11:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000355s ]
[2025-08-07 19:11:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000323s ]
[2025-08-07 19:11:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002032s ]
[2025-08-07 19:12:16][sql] CONNECT:[ UseTime:0.020659s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:12:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000667s ]
[2025-08-07 19:12:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000386s ]
[2025-08-07 19:12:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565136 [ RunTime:0.018805s ]
[2025-08-07 19:12:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000522s ]
[2025-08-07 19:12:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000243s ]
[2025-08-07 19:12:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000324s ]
[2025-08-07 19:12:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000586s ]
[2025-08-07 19:12:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000229s ]
[2025-08-07 19:12:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000341s ]
[2025-08-07 19:12:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000455s ]
[2025-08-07 19:12:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002154s ]
[2025-08-07 19:12:16][sql] CONNECT:[ UseTime:0.019616s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:12:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000902s ]
[2025-08-07 19:12:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000511s ]
[2025-08-07 19:12:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565136 [ RunTime:0.000633s ]
[2025-08-07 19:12:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000532s ]
[2025-08-07 19:12:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000244s ]
[2025-08-07 19:12:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000335s ]
[2025-08-07 19:12:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000541s ]
[2025-08-07 19:12:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000231s ]
[2025-08-07 19:12:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000412s ]
[2025-08-07 19:12:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000347s ]
[2025-08-07 19:12:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001777s ]
[2025-08-07 19:13:16][sql] CONNECT:[ UseTime:0.020651s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:13:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000769s ]
[2025-08-07 19:13:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000454s ]
[2025-08-07 19:13:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565196 [ RunTime:0.000229s ]
[2025-08-07 19:13:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000781s ]
[2025-08-07 19:13:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000398s ]
[2025-08-07 19:13:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000347s ]
[2025-08-07 19:13:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000600s ]
[2025-08-07 19:13:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000255s ]
[2025-08-07 19:13:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000384s ]
[2025-08-07 19:13:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000323s ]
[2025-08-07 19:13:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001890s ]
[2025-08-07 19:13:16][sql] CONNECT:[ UseTime:0.018933s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:13:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000786s ]
[2025-08-07 19:13:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000453s ]
[2025-08-07 19:13:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565196 [ RunTime:0.000215s ]
[2025-08-07 19:13:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000532s ]
[2025-08-07 19:13:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000245s ]
[2025-08-07 19:13:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000323s ]
[2025-08-07 19:13:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000575s ]
[2025-08-07 19:13:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000228s ]
[2025-08-07 19:13:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000353s ]
[2025-08-07 19:13:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000310s ]
[2025-08-07 19:13:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001920s ]
[2025-08-07 19:14:16][sql] CONNECT:[ UseTime:0.021457s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:14:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000736s ]
[2025-08-07 19:14:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000394s ]
[2025-08-07 19:14:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565256 [ RunTime:0.000217s ]
[2025-08-07 19:14:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000552s ]
[2025-08-07 19:14:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000252s ]
[2025-08-07 19:14:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000454s ]
[2025-08-07 19:14:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000587s ]
[2025-08-07 19:14:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000233s ]
[2025-08-07 19:14:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000338s ]
[2025-08-07 19:14:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000316s ]
[2025-08-07 19:14:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002304s ]
[2025-08-07 19:14:16][sql] CONNECT:[ UseTime:0.019517s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:14:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000837s ]
[2025-08-07 19:14:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000518s ]
[2025-08-07 19:14:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565256 [ RunTime:0.000375s ]
[2025-08-07 19:14:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000734s ]
[2025-08-07 19:14:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000269s ]
[2025-08-07 19:14:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000354s ]
[2025-08-07 19:14:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000955s ]
[2025-08-07 19:14:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000333s ]
[2025-08-07 19:14:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000367s ]
[2025-08-07 19:14:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000319s ]
[2025-08-07 19:14:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001693s ]
[2025-08-07 19:15:16][sql] CONNECT:[ UseTime:0.020078s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:15:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000746s ]
[2025-08-07 19:15:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000442s ]
[2025-08-07 19:15:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565316 [ RunTime:0.000219s ]
[2025-08-07 19:15:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000707s ]
[2025-08-07 19:15:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000296s ]
[2025-08-07 19:15:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000337s ]
[2025-08-07 19:15:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000591s ]
[2025-08-07 19:15:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000240s ]
[2025-08-07 19:15:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000338s ]
[2025-08-07 19:15:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000321s ]
[2025-08-07 19:15:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001802s ]
[2025-08-07 19:15:16][sql] CONNECT:[ UseTime:0.019672s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:15:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.001315s ]
[2025-08-07 19:15:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000559s ]
[2025-08-07 19:15:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565316 [ RunTime:0.000347s ]
[2025-08-07 19:15:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000662s ]
[2025-08-07 19:15:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000449s ]
[2025-08-07 19:15:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000452s ]
[2025-08-07 19:15:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000769s ]
[2025-08-07 19:15:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000328s ]
[2025-08-07 19:15:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000498s ]
[2025-08-07 19:15:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000378s ]
[2025-08-07 19:15:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002829s ]
[2025-08-07 19:16:16][sql] CONNECT:[ UseTime:0.018118s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:16:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000727s ]
[2025-08-07 19:16:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000406s ]
[2025-08-07 19:16:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565376 [ RunTime:0.000223s ]
[2025-08-07 19:16:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000518s ]
[2025-08-07 19:16:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000242s ]
[2025-08-07 19:16:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000334s ]
[2025-08-07 19:16:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000541s ]
[2025-08-07 19:16:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000236s ]
[2025-08-07 19:16:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000345s ]
[2025-08-07 19:16:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000338s ]
[2025-08-07 19:16:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001754s ]
[2025-08-07 19:16:16][sql] CONNECT:[ UseTime:0.017620s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:16:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000733s ]
[2025-08-07 19:16:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000404s ]
[2025-08-07 19:16:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565376 [ RunTime:0.000222s ]
[2025-08-07 19:16:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000534s ]
[2025-08-07 19:16:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000247s ]
[2025-08-07 19:16:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000346s ]
[2025-08-07 19:16:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000600s ]
[2025-08-07 19:16:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000242s ]
[2025-08-07 19:16:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000342s ]
[2025-08-07 19:16:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000338s ]
[2025-08-07 19:16:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001971s ]
[2025-08-07 19:17:16][sql] CONNECT:[ UseTime:0.018203s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:17:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000764s ]
[2025-08-07 19:17:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000399s ]
[2025-08-07 19:17:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565436 [ RunTime:0.000221s ]
[2025-08-07 19:17:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000540s ]
[2025-08-07 19:17:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000250s ]
[2025-08-07 19:17:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000429s ]
[2025-08-07 19:17:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000587s ]
[2025-08-07 19:17:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000245s ]
[2025-08-07 19:17:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000345s ]
[2025-08-07 19:17:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000321s ]
[2025-08-07 19:17:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001968s ]
[2025-08-07 19:17:16][sql] CONNECT:[ UseTime:0.017238s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:17:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000731s ]
[2025-08-07 19:17:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000409s ]
[2025-08-07 19:17:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565436 [ RunTime:0.000223s ]
[2025-08-07 19:17:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000581s ]
[2025-08-07 19:17:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000304s ]
[2025-08-07 19:17:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000436s ]
[2025-08-07 19:17:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000589s ]
[2025-08-07 19:17:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000279s ]
[2025-08-07 19:17:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000341s ]
[2025-08-07 19:17:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000307s ]
[2025-08-07 19:17:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001962s ]
[2025-08-07 19:18:16][sql] CONNECT:[ UseTime:0.020102s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:18:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000890s ]
[2025-08-07 19:18:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000419s ]
[2025-08-07 19:18:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565496 [ RunTime:0.015365s ]
[2025-08-07 19:18:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000543s ]
[2025-08-07 19:18:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000293s ]
[2025-08-07 19:18:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000343s ]
[2025-08-07 19:18:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000666s ]
[2025-08-07 19:18:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000228s ]
[2025-08-07 19:18:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000341s ]
[2025-08-07 19:18:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000467s ]
[2025-08-07 19:18:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002113s ]
[2025-08-07 19:18:16][sql] CONNECT:[ UseTime:0.018923s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:18:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000878s ]
[2025-08-07 19:18:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000425s ]
[2025-08-07 19:18:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565496 [ RunTime:0.006829s ]
[2025-08-07 19:18:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000638s ]
[2025-08-07 19:18:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000253s ]
[2025-08-07 19:18:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000346s ]
[2025-08-07 19:18:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000577s ]
[2025-08-07 19:18:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000228s ]
[2025-08-07 19:18:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000350s ]
[2025-08-07 19:18:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000311s ]
[2025-08-07 19:18:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001729s ]
[2025-08-07 19:19:16][sql] CONNECT:[ UseTime:0.019088s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:19:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000724s ]
[2025-08-07 19:19:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000398s ]
[2025-08-07 19:19:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565556 [ RunTime:0.017920s ]
[2025-08-07 19:19:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000519s ]
[2025-08-07 19:19:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000323s ]
[2025-08-07 19:19:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000366s ]
[2025-08-07 19:19:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000542s ]
[2025-08-07 19:19:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000268s ]
[2025-08-07 19:19:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000524s ]
[2025-08-07 19:19:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000525s ]
[2025-08-07 19:19:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002157s ]
[2025-08-07 19:19:16][sql] CONNECT:[ UseTime:0.017371s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:19:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000686s ]
[2025-08-07 19:19:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000409s ]
[2025-08-07 19:19:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565556 [ RunTime:0.000241s ]
[2025-08-07 19:19:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000521s ]
[2025-08-07 19:19:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000245s ]
[2025-08-07 19:19:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000401s ]
[2025-08-07 19:19:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000588s ]
[2025-08-07 19:19:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000226s ]
[2025-08-07 19:19:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000338s ]
[2025-08-07 19:19:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000319s ]
[2025-08-07 19:19:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001942s ]
[2025-08-07 19:20:16][sql] CONNECT:[ UseTime:0.020545s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:20:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000752s ]
[2025-08-07 19:20:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000411s ]
[2025-08-07 19:20:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565616 [ RunTime:0.000242s ]
[2025-08-07 19:20:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000626s ]
[2025-08-07 19:20:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000252s ]
[2025-08-07 19:20:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000332s ]
[2025-08-07 19:20:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000629s ]
[2025-08-07 19:20:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000259s ]
[2025-08-07 19:20:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000357s ]
[2025-08-07 19:20:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000332s ]
[2025-08-07 19:20:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002536s ]
[2025-08-07 19:20:16][sql] CONNECT:[ UseTime:0.017905s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:20:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000761s ]
[2025-08-07 19:20:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000406s ]
[2025-08-07 19:20:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565616 [ RunTime:0.000218s ]
[2025-08-07 19:20:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000518s ]
[2025-08-07 19:20:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000305s ]
[2025-08-07 19:20:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000327s ]
[2025-08-07 19:20:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000584s ]
[2025-08-07 19:20:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000231s ]
[2025-08-07 19:20:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000347s ]
[2025-08-07 19:20:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000395s ]
[2025-08-07 19:20:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001695s ]
[2025-08-07 19:21:16][sql] CONNECT:[ UseTime:0.021087s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:21:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000719s ]
[2025-08-07 19:21:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000402s ]
[2025-08-07 19:21:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565676 [ RunTime:0.000221s ]
[2025-08-07 19:21:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000533s ]
[2025-08-07 19:21:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000249s ]
[2025-08-07 19:21:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000345s ]
[2025-08-07 19:21:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000581s ]
[2025-08-07 19:21:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000233s ]
[2025-08-07 19:21:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000355s ]
[2025-08-07 19:21:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000331s ]
[2025-08-07 19:21:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001778s ]
[2025-08-07 19:21:16][sql] CONNECT:[ UseTime:0.019929s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:21:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000777s ]
[2025-08-07 19:21:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000392s ]
[2025-08-07 19:21:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565676 [ RunTime:0.000287s ]
[2025-08-07 19:21:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000529s ]
[2025-08-07 19:21:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000270s ]
[2025-08-07 19:21:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000342s ]
[2025-08-07 19:21:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000602s ]
[2025-08-07 19:21:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000255s ]
[2025-08-07 19:21:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000383s ]
[2025-08-07 19:21:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000316s ]
[2025-08-07 19:21:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001787s ]
[2025-08-07 19:22:16][sql] CONNECT:[ UseTime:0.018011s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:22:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000683s ]
[2025-08-07 19:22:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000410s ]
[2025-08-07 19:22:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565736 [ RunTime:0.000220s ]
[2025-08-07 19:22:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000534s ]
[2025-08-07 19:22:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000245s ]
[2025-08-07 19:22:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000328s ]
[2025-08-07 19:22:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000579s ]
[2025-08-07 19:22:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000233s ]
[2025-08-07 19:22:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000368s ]
[2025-08-07 19:22:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000314s ]
[2025-08-07 19:22:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001846s ]
[2025-08-07 19:22:16][sql] CONNECT:[ UseTime:0.021314s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:22:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000732s ]
[2025-08-07 19:22:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000441s ]
[2025-08-07 19:22:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565736 [ RunTime:0.000227s ]
[2025-08-07 19:22:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000539s ]
[2025-08-07 19:22:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000247s ]
[2025-08-07 19:22:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000331s ]
[2025-08-07 19:22:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000623s ]
[2025-08-07 19:22:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000226s ]
[2025-08-07 19:22:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000397s ]
[2025-08-07 19:22:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000317s ]
[2025-08-07 19:22:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001796s ]
[2025-08-07 19:23:16][sql] CONNECT:[ UseTime:0.020869s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:23:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000824s ]
[2025-08-07 19:23:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000381s ]
[2025-08-07 19:23:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565796 [ RunTime:0.000211s ]
[2025-08-07 19:23:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000521s ]
[2025-08-07 19:23:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000374s ]
[2025-08-07 19:23:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000355s ]
[2025-08-07 19:23:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000559s ]
[2025-08-07 19:23:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000232s ]
[2025-08-07 19:23:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000346s ]
[2025-08-07 19:23:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000317s ]
[2025-08-07 19:23:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001748s ]
[2025-08-07 19:23:16][sql] CONNECT:[ UseTime:0.019808s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:23:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000763s ]
[2025-08-07 19:23:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000419s ]
[2025-08-07 19:23:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565796 [ RunTime:0.000225s ]
[2025-08-07 19:23:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000510s ]
[2025-08-07 19:23:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000245s ]
[2025-08-07 19:23:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000494s ]
[2025-08-07 19:23:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000686s ]
[2025-08-07 19:23:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000263s ]
[2025-08-07 19:23:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000358s ]
[2025-08-07 19:23:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000328s ]
[2025-08-07 19:23:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001751s ]
[2025-08-07 19:24:16][sql] CONNECT:[ UseTime:0.022199s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:24:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000756s ]
[2025-08-07 19:24:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000393s ]
[2025-08-07 19:24:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565856 [ RunTime:0.000214s ]
[2025-08-07 19:24:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000541s ]
[2025-08-07 19:24:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000246s ]
[2025-08-07 19:24:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000339s ]
[2025-08-07 19:24:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000583s ]
[2025-08-07 19:24:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000231s ]
[2025-08-07 19:24:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000356s ]
[2025-08-07 19:24:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000312s ]
[2025-08-07 19:24:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001682s ]
[2025-08-07 19:24:16][sql] CONNECT:[ UseTime:0.019974s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:24:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000857s ]
[2025-08-07 19:24:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000406s ]
[2025-08-07 19:24:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565856 [ RunTime:0.000225s ]
[2025-08-07 19:24:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000681s ]
[2025-08-07 19:24:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000256s ]
[2025-08-07 19:24:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000343s ]
[2025-08-07 19:24:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000591s ]
[2025-08-07 19:24:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000244s ]
[2025-08-07 19:24:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000351s ]
[2025-08-07 19:24:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000385s ]
[2025-08-07 19:24:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001888s ]
[2025-08-07 19:25:16][sql] CONNECT:[ UseTime:0.021128s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:25:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000727s ]
[2025-08-07 19:25:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000391s ]
[2025-08-07 19:25:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565916 [ RunTime:0.000294s ]
[2025-08-07 19:25:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000546s ]
[2025-08-07 19:25:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000250s ]
[2025-08-07 19:25:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000339s ]
[2025-08-07 19:25:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000585s ]
[2025-08-07 19:25:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000245s ]
[2025-08-07 19:25:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000350s ]
[2025-08-07 19:25:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000327s ]
[2025-08-07 19:25:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001857s ]
[2025-08-07 19:25:16][sql] CONNECT:[ UseTime:0.019150s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:25:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000729s ]
[2025-08-07 19:25:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000416s ]
[2025-08-07 19:25:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565916 [ RunTime:0.000383s ]
[2025-08-07 19:25:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000548s ]
[2025-08-07 19:25:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000320s ]
[2025-08-07 19:25:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000339s ]
[2025-08-07 19:25:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000571s ]
[2025-08-07 19:25:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000224s ]
[2025-08-07 19:25:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000341s ]
[2025-08-07 19:25:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000320s ]
[2025-08-07 19:25:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001940s ]
[2025-08-07 19:26:16][sql] CONNECT:[ UseTime:0.020721s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:26:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000777s ]
[2025-08-07 19:26:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000421s ]
[2025-08-07 19:26:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565976 [ RunTime:0.000364s ]
[2025-08-07 19:26:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000498s ]
[2025-08-07 19:26:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000257s ]
[2025-08-07 19:26:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000332s ]
[2025-08-07 19:26:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000601s ]
[2025-08-07 19:26:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000231s ]
[2025-08-07 19:26:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000516s ]
[2025-08-07 19:26:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000350s ]
[2025-08-07 19:26:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001700s ]
[2025-08-07 19:26:16][sql] CONNECT:[ UseTime:0.020074s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:26:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000764s ]
[2025-08-07 19:26:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000393s ]
[2025-08-07 19:26:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754565976 [ RunTime:0.000217s ]
[2025-08-07 19:26:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000545s ]
[2025-08-07 19:26:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000259s ]
[2025-08-07 19:26:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000335s ]
[2025-08-07 19:26:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000586s ]
[2025-08-07 19:26:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000231s ]
[2025-08-07 19:26:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000345s ]
[2025-08-07 19:26:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000321s ]
[2025-08-07 19:26:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001700s ]
[2025-08-07 19:27:16][sql] CONNECT:[ UseTime:0.019715s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:27:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000727s ]
[2025-08-07 19:27:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000399s ]
[2025-08-07 19:27:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566036 [ RunTime:0.000220s ]
[2025-08-07 19:27:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000704s ]
[2025-08-07 19:27:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000408s ]
[2025-08-07 19:27:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000409s ]
[2025-08-07 19:27:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000599s ]
[2025-08-07 19:27:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000239s ]
[2025-08-07 19:27:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000344s ]
[2025-08-07 19:27:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000309s ]
[2025-08-07 19:27:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001707s ]
[2025-08-07 19:27:16][sql] CONNECT:[ UseTime:0.020379s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:27:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000739s ]
[2025-08-07 19:27:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000452s ]
[2025-08-07 19:27:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566036 [ RunTime:0.000243s ]
[2025-08-07 19:27:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000769s ]
[2025-08-07 19:27:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000249s ]
[2025-08-07 19:27:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000332s ]
[2025-08-07 19:27:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000560s ]
[2025-08-07 19:27:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000227s ]
[2025-08-07 19:27:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000344s ]
[2025-08-07 19:27:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000324s ]
[2025-08-07 19:27:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001863s ]
[2025-08-07 19:28:16][sql] CONNECT:[ UseTime:0.018233s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:28:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000784s ]
[2025-08-07 19:28:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000577s ]
[2025-08-07 19:28:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566096 [ RunTime:0.000235s ]
[2025-08-07 19:28:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000581s ]
[2025-08-07 19:28:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000259s ]
[2025-08-07 19:28:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000412s ]
[2025-08-07 19:28:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000590s ]
[2025-08-07 19:28:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000251s ]
[2025-08-07 19:28:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000348s ]
[2025-08-07 19:28:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000319s ]
[2025-08-07 19:28:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001729s ]
[2025-08-07 19:28:16][sql] CONNECT:[ UseTime:0.019414s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:28:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000733s ]
[2025-08-07 19:28:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000438s ]
[2025-08-07 19:28:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566096 [ RunTime:0.000220s ]
[2025-08-07 19:28:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000529s ]
[2025-08-07 19:28:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000249s ]
[2025-08-07 19:28:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000323s ]
[2025-08-07 19:28:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000570s ]
[2025-08-07 19:28:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000224s ]
[2025-08-07 19:28:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000337s ]
[2025-08-07 19:28:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000586s ]
[2025-08-07 19:28:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001916s ]
[2025-08-07 19:29:16][sql] CONNECT:[ UseTime:0.018703s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:29:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000756s ]
[2025-08-07 19:29:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000399s ]
[2025-08-07 19:29:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566156 [ RunTime:0.000222s ]
[2025-08-07 19:29:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000957s ]
[2025-08-07 19:29:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000268s ]
[2025-08-07 19:29:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000347s ]
[2025-08-07 19:29:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000696s ]
[2025-08-07 19:29:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000230s ]
[2025-08-07 19:29:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000341s ]
[2025-08-07 19:29:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000310s ]
[2025-08-07 19:29:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002432s ]
[2025-08-07 19:29:16][sql] CONNECT:[ UseTime:0.021469s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:29:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000822s ]
[2025-08-07 19:29:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000400s ]
[2025-08-07 19:29:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566156 [ RunTime:0.000231s ]
[2025-08-07 19:29:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000635s ]
[2025-08-07 19:29:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000275s ]
[2025-08-07 19:29:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000356s ]
[2025-08-07 19:29:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000592s ]
[2025-08-07 19:29:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000374s ]
[2025-08-07 19:29:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000381s ]
[2025-08-07 19:29:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000321s ]
[2025-08-07 19:29:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001725s ]
[2025-08-07 19:30:16][sql] CONNECT:[ UseTime:0.018585s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:30:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000708s ]
[2025-08-07 19:30:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000411s ]
[2025-08-07 19:30:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566216 [ RunTime:0.000226s ]
[2025-08-07 19:30:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000560s ]
[2025-08-07 19:30:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000248s ]
[2025-08-07 19:30:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000325s ]
[2025-08-07 19:30:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000586s ]
[2025-08-07 19:30:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000244s ]
[2025-08-07 19:30:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000393s ]
[2025-08-07 19:30:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000310s ]
[2025-08-07 19:30:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001845s ]
[2025-08-07 19:30:16][sql] CONNECT:[ UseTime:0.020743s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:30:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000753s ]
[2025-08-07 19:30:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000424s ]
[2025-08-07 19:30:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566216 [ RunTime:0.000334s ]
[2025-08-07 19:30:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000797s ]
[2025-08-07 19:30:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000267s ]
[2025-08-07 19:30:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000355s ]
[2025-08-07 19:30:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000667s ]
[2025-08-07 19:30:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000235s ]
[2025-08-07 19:30:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000349s ]
[2025-08-07 19:30:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000387s ]
[2025-08-07 19:30:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001732s ]
[2025-08-07 19:31:16][sql] CONNECT:[ UseTime:0.019948s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:31:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000678s ]
[2025-08-07 19:31:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000397s ]
[2025-08-07 19:31:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566276 [ RunTime:0.000217s ]
[2025-08-07 19:31:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000603s ]
[2025-08-07 19:31:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000262s ]
[2025-08-07 19:31:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000327s ]
[2025-08-07 19:31:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000635s ]
[2025-08-07 19:31:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000235s ]
[2025-08-07 19:31:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000346s ]
[2025-08-07 19:31:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000324s ]
[2025-08-07 19:31:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001710s ]
[2025-08-07 19:31:16][sql] CONNECT:[ UseTime:0.020700s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:31:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000736s ]
[2025-08-07 19:31:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000439s ]
[2025-08-07 19:31:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566276 [ RunTime:0.013224s ]
[2025-08-07 19:31:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000503s ]
[2025-08-07 19:31:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000250s ]
[2025-08-07 19:31:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000338s ]
[2025-08-07 19:31:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000579s ]
[2025-08-07 19:31:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000227s ]
[2025-08-07 19:31:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000340s ]
[2025-08-07 19:31:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000588s ]
[2025-08-07 19:31:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.003312s ]
[2025-08-07 19:32:16][sql] CONNECT:[ UseTime:0.020534s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:32:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000877s ]
[2025-08-07 19:32:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000511s ]
[2025-08-07 19:32:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566336 [ RunTime:0.000218s ]
[2025-08-07 19:32:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000547s ]
[2025-08-07 19:32:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000248s ]
[2025-08-07 19:32:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000336s ]
[2025-08-07 19:32:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000655s ]
[2025-08-07 19:32:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000261s ]
[2025-08-07 19:32:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000367s ]
[2025-08-07 19:32:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000408s ]
[2025-08-07 19:32:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001787s ]
[2025-08-07 19:32:16][sql] CONNECT:[ UseTime:0.020141s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:32:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000733s ]
[2025-08-07 19:32:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000400s ]
[2025-08-07 19:32:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566336 [ RunTime:0.000293s ]
[2025-08-07 19:32:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000526s ]
[2025-08-07 19:32:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000245s ]
[2025-08-07 19:32:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000340s ]
[2025-08-07 19:32:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000621s ]
[2025-08-07 19:32:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000230s ]
[2025-08-07 19:32:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000468s ]
[2025-08-07 19:32:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000429s ]
[2025-08-07 19:32:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001843s ]
[2025-08-07 19:33:16][sql] CONNECT:[ UseTime:0.020575s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:33:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000724s ]
[2025-08-07 19:33:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000407s ]
[2025-08-07 19:33:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566396 [ RunTime:0.000456s ]
[2025-08-07 19:33:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000534s ]
[2025-08-07 19:33:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000242s ]
[2025-08-07 19:33:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000340s ]
[2025-08-07 19:33:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000571s ]
[2025-08-07 19:33:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000268s ]
[2025-08-07 19:33:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000357s ]
[2025-08-07 19:33:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000322s ]
[2025-08-07 19:33:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001744s ]
[2025-08-07 19:33:16][sql] CONNECT:[ UseTime:0.021931s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:33:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000929s ]
[2025-08-07 19:33:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000406s ]
[2025-08-07 19:33:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566396 [ RunTime:0.000223s ]
[2025-08-07 19:33:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000548s ]
[2025-08-07 19:33:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000255s ]
[2025-08-07 19:33:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000342s ]
[2025-08-07 19:33:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000589s ]
[2025-08-07 19:33:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000234s ]
[2025-08-07 19:33:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000346s ]
[2025-08-07 19:33:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000326s ]
[2025-08-07 19:33:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001745s ]
[2025-08-07 19:34:16][sql] CONNECT:[ UseTime:0.020015s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:34:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000745s ]
[2025-08-07 19:34:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000410s ]
[2025-08-07 19:34:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566456 [ RunTime:0.000221s ]
[2025-08-07 19:34:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000638s ]
[2025-08-07 19:34:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000314s ]
[2025-08-07 19:34:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000473s ]
[2025-08-07 19:34:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000632s ]
[2025-08-07 19:34:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000400s ]
[2025-08-07 19:34:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000499s ]
[2025-08-07 19:34:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000442s ]
[2025-08-07 19:34:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001813s ]
[2025-08-07 19:34:16][sql] CONNECT:[ UseTime:0.015752s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:34:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000776s ]
[2025-08-07 19:34:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000563s ]
[2025-08-07 19:34:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566456 [ RunTime:0.000219s ]
[2025-08-07 19:34:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000553s ]
[2025-08-07 19:34:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000248s ]
[2025-08-07 19:34:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000329s ]
[2025-08-07 19:34:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000590s ]
[2025-08-07 19:34:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000227s ]
[2025-08-07 19:34:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000394s ]
[2025-08-07 19:34:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000320s ]
[2025-08-07 19:34:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002211s ]
[2025-08-07 19:35:16][sql] CONNECT:[ UseTime:0.020082s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:35:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000918s ]
[2025-08-07 19:35:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000403s ]
[2025-08-07 19:35:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566516 [ RunTime:0.000268s ]
[2025-08-07 19:35:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000522s ]
[2025-08-07 19:35:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000257s ]
[2025-08-07 19:35:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000555s ]
[2025-08-07 19:35:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000597s ]
[2025-08-07 19:35:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000238s ]
[2025-08-07 19:35:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000347s ]
[2025-08-07 19:35:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000336s ]
[2025-08-07 19:35:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002121s ]
[2025-08-07 19:35:16][sql] CONNECT:[ UseTime:0.019518s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:35:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000761s ]
[2025-08-07 19:35:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000404s ]
[2025-08-07 19:35:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566516 [ RunTime:0.000250s ]
[2025-08-07 19:35:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000525s ]
[2025-08-07 19:35:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000254s ]
[2025-08-07 19:35:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000333s ]
[2025-08-07 19:35:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000622s ]
[2025-08-07 19:35:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000237s ]
[2025-08-07 19:35:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000342s ]
[2025-08-07 19:35:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000313s ]
[2025-08-07 19:35:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001873s ]
[2025-08-07 19:36:16][sql] CONNECT:[ UseTime:0.019735s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:36:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.001073s ]
[2025-08-07 19:36:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000622s ]
[2025-08-07 19:36:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566576 [ RunTime:0.000338s ]
[2025-08-07 19:36:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000527s ]
[2025-08-07 19:36:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000246s ]
[2025-08-07 19:36:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000329s ]
[2025-08-07 19:36:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000579s ]
[2025-08-07 19:36:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000225s ]
[2025-08-07 19:36:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000339s ]
[2025-08-07 19:36:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000311s ]
[2025-08-07 19:36:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001872s ]
[2025-08-07 19:36:16][sql] CONNECT:[ UseTime:0.018137s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:36:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000713s ]
[2025-08-07 19:36:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000389s ]
[2025-08-07 19:36:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566576 [ RunTime:0.000313s ]
[2025-08-07 19:36:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000543s ]
[2025-08-07 19:36:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000249s ]
[2025-08-07 19:36:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000337s ]
[2025-08-07 19:36:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000585s ]
[2025-08-07 19:36:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000237s ]
[2025-08-07 19:36:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000345s ]
[2025-08-07 19:36:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000317s ]
[2025-08-07 19:36:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001785s ]
[2025-08-07 19:37:16][sql] CONNECT:[ UseTime:0.020821s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:37:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000691s ]
[2025-08-07 19:37:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000405s ]
[2025-08-07 19:37:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566636 [ RunTime:0.000223s ]
[2025-08-07 19:37:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000540s ]
[2025-08-07 19:37:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000249s ]
[2025-08-07 19:37:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000344s ]
[2025-08-07 19:37:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000584s ]
[2025-08-07 19:37:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000338s ]
[2025-08-07 19:37:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000592s ]
[2025-08-07 19:37:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000346s ]
[2025-08-07 19:37:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001983s ]
[2025-08-07 19:37:16][sql] CONNECT:[ UseTime:0.019707s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:37:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000739s ]
[2025-08-07 19:37:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000405s ]
[2025-08-07 19:37:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566636 [ RunTime:0.000240s ]
[2025-08-07 19:37:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000543s ]
[2025-08-07 19:37:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000245s ]
[2025-08-07 19:37:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000439s ]
[2025-08-07 19:37:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000608s ]
[2025-08-07 19:37:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000230s ]
[2025-08-07 19:37:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000342s ]
[2025-08-07 19:37:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000306s ]
[2025-08-07 19:37:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001863s ]
[2025-08-07 19:38:16][sql] CONNECT:[ UseTime:0.020773s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:38:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000732s ]
[2025-08-07 19:38:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000398s ]
[2025-08-07 19:38:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566696 [ RunTime:0.000226s ]
[2025-08-07 19:38:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000519s ]
[2025-08-07 19:38:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000246s ]
[2025-08-07 19:38:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000454s ]
[2025-08-07 19:38:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000619s ]
[2025-08-07 19:38:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000232s ]
[2025-08-07 19:38:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000347s ]
[2025-08-07 19:38:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000442s ]
[2025-08-07 19:38:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001797s ]
[2025-08-07 19:38:16][sql] CONNECT:[ UseTime:0.020560s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:38:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000742s ]
[2025-08-07 19:38:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000399s ]
[2025-08-07 19:38:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566696 [ RunTime:0.000226s ]
[2025-08-07 19:38:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000566s ]
[2025-08-07 19:38:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000305s ]
[2025-08-07 19:38:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000333s ]
[2025-08-07 19:38:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000639s ]
[2025-08-07 19:38:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000287s ]
[2025-08-07 19:38:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000371s ]
[2025-08-07 19:38:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000334s ]
[2025-08-07 19:38:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001871s ]
[2025-08-07 19:39:16][sql] CONNECT:[ UseTime:0.018996s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:39:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000792s ]
[2025-08-07 19:39:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000390s ]
[2025-08-07 19:39:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566756 [ RunTime:0.000252s ]
[2025-08-07 19:39:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000532s ]
[2025-08-07 19:39:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000245s ]
[2025-08-07 19:39:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000331s ]
[2025-08-07 19:39:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000570s ]
[2025-08-07 19:39:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000231s ]
[2025-08-07 19:39:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000340s ]
[2025-08-07 19:39:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000319s ]
[2025-08-07 19:39:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001684s ]
[2025-08-07 19:39:16][sql] CONNECT:[ UseTime:0.021155s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:39:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000683s ]
[2025-08-07 19:39:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000400s ]
[2025-08-07 19:39:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566756 [ RunTime:0.000220s ]
[2025-08-07 19:39:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000548s ]
[2025-08-07 19:39:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000250s ]
[2025-08-07 19:39:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000339s ]
[2025-08-07 19:39:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000579s ]
[2025-08-07 19:39:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000232s ]
[2025-08-07 19:39:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000342s ]
[2025-08-07 19:39:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000311s ]
[2025-08-07 19:39:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001699s ]
[2025-08-07 19:40:16][sql] CONNECT:[ UseTime:0.018174s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:40:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000735s ]
[2025-08-07 19:40:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000434s ]
[2025-08-07 19:40:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566816 [ RunTime:0.000218s ]
[2025-08-07 19:40:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000526s ]
[2025-08-07 19:40:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000260s ]
[2025-08-07 19:40:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000338s ]
[2025-08-07 19:40:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000596s ]
[2025-08-07 19:40:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000236s ]
[2025-08-07 19:40:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000348s ]
[2025-08-07 19:40:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000314s ]
[2025-08-07 19:40:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001707s ]
[2025-08-07 19:40:16][sql] CONNECT:[ UseTime:0.019525s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:40:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000734s ]
[2025-08-07 19:40:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000403s ]
[2025-08-07 19:40:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566816 [ RunTime:0.000235s ]
[2025-08-07 19:40:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000521s ]
[2025-08-07 19:40:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000238s ]
[2025-08-07 19:40:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000345s ]
[2025-08-07 19:40:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000548s ]
[2025-08-07 19:40:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000226s ]
[2025-08-07 19:40:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000408s ]
[2025-08-07 19:40:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000313s ]
[2025-08-07 19:40:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001855s ]
[2025-08-07 19:41:16][sql] CONNECT:[ UseTime:0.022040s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:41:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000774s ]
[2025-08-07 19:41:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000530s ]
[2025-08-07 19:41:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566876 [ RunTime:0.000222s ]
[2025-08-07 19:41:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000506s ]
[2025-08-07 19:41:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000244s ]
[2025-08-07 19:41:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000334s ]
[2025-08-07 19:41:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000584s ]
[2025-08-07 19:41:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000228s ]
[2025-08-07 19:41:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000339s ]
[2025-08-07 19:41:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000308s ]
[2025-08-07 19:41:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001704s ]
[2025-08-07 19:41:16][sql] CONNECT:[ UseTime:0.020254s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:41:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000909s ]
[2025-08-07 19:41:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000399s ]
[2025-08-07 19:41:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566876 [ RunTime:0.000218s ]
[2025-08-07 19:41:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000550s ]
[2025-08-07 19:41:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000406s ]
[2025-08-07 19:41:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000399s ]
[2025-08-07 19:41:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000596s ]
[2025-08-07 19:41:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000244s ]
[2025-08-07 19:41:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000442s ]
[2025-08-07 19:41:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000334s ]
[2025-08-07 19:41:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001713s ]
[2025-08-07 19:42:16][sql] CONNECT:[ UseTime:0.020581s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:42:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000746s ]
[2025-08-07 19:42:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000417s ]
[2025-08-07 19:42:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566936 [ RunTime:0.000218s ]
[2025-08-07 19:42:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000524s ]
[2025-08-07 19:42:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000244s ]
[2025-08-07 19:42:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000329s ]
[2025-08-07 19:42:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000574s ]
[2025-08-07 19:42:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000224s ]
[2025-08-07 19:42:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000333s ]
[2025-08-07 19:42:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000308s ]
[2025-08-07 19:42:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001720s ]
[2025-08-07 19:42:16][sql] CONNECT:[ UseTime:0.021313s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:42:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000765s ]
[2025-08-07 19:42:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000445s ]
[2025-08-07 19:42:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566936 [ RunTime:0.000289s ]
[2025-08-07 19:42:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000649s ]
[2025-08-07 19:42:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000249s ]
[2025-08-07 19:42:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000330s ]
[2025-08-07 19:42:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000582s ]
[2025-08-07 19:42:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000223s ]
[2025-08-07 19:42:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000337s ]
[2025-08-07 19:42:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000325s ]
[2025-08-07 19:42:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001972s ]
[2025-08-07 19:43:16][sql] CONNECT:[ UseTime:0.021184s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:43:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000681s ]
[2025-08-07 19:43:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000393s ]
[2025-08-07 19:43:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566996 [ RunTime:0.000218s ]
[2025-08-07 19:43:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000585s ]
[2025-08-07 19:43:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000364s ]
[2025-08-07 19:43:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000395s ]
[2025-08-07 19:43:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000604s ]
[2025-08-07 19:43:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000233s ]
[2025-08-07 19:43:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000362s ]
[2025-08-07 19:43:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000434s ]
[2025-08-07 19:43:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001712s ]
[2025-08-07 19:43:16][sql] CONNECT:[ UseTime:0.020684s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:43:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000685s ]
[2025-08-07 19:43:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000418s ]
[2025-08-07 19:43:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754566996 [ RunTime:0.000218s ]
[2025-08-07 19:43:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000575s ]
[2025-08-07 19:43:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000253s ]
[2025-08-07 19:43:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000320s ]
[2025-08-07 19:43:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000583s ]
[2025-08-07 19:43:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000232s ]
[2025-08-07 19:43:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000341s ]
[2025-08-07 19:43:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000323s ]
[2025-08-07 19:43:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001832s ]
[2025-08-07 19:44:16][sql] CONNECT:[ UseTime:0.020407s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:44:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000771s ]
[2025-08-07 19:44:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000418s ]
[2025-08-07 19:44:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567056 [ RunTime:0.000239s ]
[2025-08-07 19:44:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000531s ]
[2025-08-07 19:44:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000255s ]
[2025-08-07 19:44:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000330s ]
[2025-08-07 19:44:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000580s ]
[2025-08-07 19:44:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000234s ]
[2025-08-07 19:44:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000343s ]
[2025-08-07 19:44:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000316s ]
[2025-08-07 19:44:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001792s ]
[2025-08-07 19:44:16][sql] CONNECT:[ UseTime:0.020838s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:44:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000738s ]
[2025-08-07 19:44:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000397s ]
[2025-08-07 19:44:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567056 [ RunTime:0.000280s ]
[2025-08-07 19:44:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000619s ]
[2025-08-07 19:44:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000243s ]
[2025-08-07 19:44:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000556s ]
[2025-08-07 19:44:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000580s ]
[2025-08-07 19:44:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000272s ]
[2025-08-07 19:44:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000352s ]
[2025-08-07 19:44:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000327s ]
[2025-08-07 19:44:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001733s ]
[2025-08-07 19:45:16][sql] CONNECT:[ UseTime:0.019011s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:45:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000727s ]
[2025-08-07 19:45:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000516s ]
[2025-08-07 19:45:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567116 [ RunTime:0.000227s ]
[2025-08-07 19:45:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000529s ]
[2025-08-07 19:45:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000251s ]
[2025-08-07 19:45:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000330s ]
[2025-08-07 19:45:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000573s ]
[2025-08-07 19:45:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000227s ]
[2025-08-07 19:45:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000341s ]
[2025-08-07 19:45:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000311s ]
[2025-08-07 19:45:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001913s ]
[2025-08-07 19:45:16][sql] CONNECT:[ UseTime:0.021097s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:45:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.001083s ]
[2025-08-07 19:45:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000410s ]
[2025-08-07 19:45:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567116 [ RunTime:0.000226s ]
[2025-08-07 19:45:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000511s ]
[2025-08-07 19:45:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000395s ]
[2025-08-07 19:45:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000359s ]
[2025-08-07 19:45:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000606s ]
[2025-08-07 19:45:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000235s ]
[2025-08-07 19:45:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000364s ]
[2025-08-07 19:45:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000323s ]
[2025-08-07 19:45:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001707s ]
[2025-08-07 19:46:16][sql] CONNECT:[ UseTime:0.020919s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:46:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000685s ]
[2025-08-07 19:46:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000489s ]
[2025-08-07 19:46:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567176 [ RunTime:0.000238s ]
[2025-08-07 19:46:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000542s ]
[2025-08-07 19:46:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000254s ]
[2025-08-07 19:46:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000339s ]
[2025-08-07 19:46:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000603s ]
[2025-08-07 19:46:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000256s ]
[2025-08-07 19:46:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000418s ]
[2025-08-07 19:46:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000314s ]
[2025-08-07 19:46:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001875s ]
[2025-08-07 19:46:16][sql] CONNECT:[ UseTime:0.017458s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:46:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000738s ]
[2025-08-07 19:46:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000392s ]
[2025-08-07 19:46:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567176 [ RunTime:0.000241s ]
[2025-08-07 19:46:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000523s ]
[2025-08-07 19:46:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000239s ]
[2025-08-07 19:46:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000387s ]
[2025-08-07 19:46:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000538s ]
[2025-08-07 19:46:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000230s ]
[2025-08-07 19:46:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000342s ]
[2025-08-07 19:46:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000315s ]
[2025-08-07 19:46:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001716s ]
[2025-08-07 19:47:16][sql] CONNECT:[ UseTime:0.018327s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:47:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000769s ]
[2025-08-07 19:47:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000403s ]
[2025-08-07 19:47:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567236 [ RunTime:0.000221s ]
[2025-08-07 19:47:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000541s ]
[2025-08-07 19:47:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000261s ]
[2025-08-07 19:47:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000394s ]
[2025-08-07 19:47:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000614s ]
[2025-08-07 19:47:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000263s ]
[2025-08-07 19:47:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000377s ]
[2025-08-07 19:47:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000353s ]
[2025-08-07 19:47:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002048s ]
[2025-08-07 19:47:16][sql] CONNECT:[ UseTime:0.020350s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:47:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000729s ]
[2025-08-07 19:47:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000405s ]
[2025-08-07 19:47:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567236 [ RunTime:0.000214s ]
[2025-08-07 19:47:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000518s ]
[2025-08-07 19:47:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000251s ]
[2025-08-07 19:47:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000338s ]
[2025-08-07 19:47:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000585s ]
[2025-08-07 19:47:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000228s ]
[2025-08-07 19:47:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000344s ]
[2025-08-07 19:47:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000330s ]
[2025-08-07 19:47:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001853s ]
[2025-08-07 19:48:16][sql] CONNECT:[ UseTime:0.019486s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:48:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000802s ]
[2025-08-07 19:48:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000398s ]
[2025-08-07 19:48:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567296 [ RunTime:0.000216s ]
[2025-08-07 19:48:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000528s ]
[2025-08-07 19:48:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000253s ]
[2025-08-07 19:48:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000656s ]
[2025-08-07 19:48:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000724s ]
[2025-08-07 19:48:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000375s ]
[2025-08-07 19:48:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000399s ]
[2025-08-07 19:48:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000332s ]
[2025-08-07 19:48:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001924s ]
[2025-08-07 19:48:16][sql] CONNECT:[ UseTime:0.021279s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:48:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000697s ]
[2025-08-07 19:48:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000390s ]
[2025-08-07 19:48:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567296 [ RunTime:0.000208s ]
[2025-08-07 19:48:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000509s ]
[2025-08-07 19:48:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000244s ]
[2025-08-07 19:48:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000339s ]
[2025-08-07 19:48:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000586s ]
[2025-08-07 19:48:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000305s ]
[2025-08-07 19:48:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000401s ]
[2025-08-07 19:48:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000432s ]
[2025-08-07 19:48:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001827s ]
[2025-08-07 19:49:16][sql] CONNECT:[ UseTime:0.019284s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:49:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000732s ]
[2025-08-07 19:49:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000423s ]
[2025-08-07 19:49:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567356 [ RunTime:0.000222s ]
[2025-08-07 19:49:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000540s ]
[2025-08-07 19:49:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000249s ]
[2025-08-07 19:49:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000339s ]
[2025-08-07 19:49:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000579s ]
[2025-08-07 19:49:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000231s ]
[2025-08-07 19:49:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000370s ]
[2025-08-07 19:49:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000325s ]
[2025-08-07 19:49:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001820s ]
[2025-08-07 19:49:16][sql] CONNECT:[ UseTime:0.018476s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:49:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000723s ]
[2025-08-07 19:49:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000401s ]
[2025-08-07 19:49:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567356 [ RunTime:0.000277s ]
[2025-08-07 19:49:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000538s ]
[2025-08-07 19:49:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000247s ]
[2025-08-07 19:49:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000342s ]
[2025-08-07 19:49:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000599s ]
[2025-08-07 19:49:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000229s ]
[2025-08-07 19:49:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000343s ]
[2025-08-07 19:49:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000313s ]
[2025-08-07 19:49:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001899s ]
[2025-08-07 19:50:16][sql] CONNECT:[ UseTime:0.022560s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:50:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000726s ]
[2025-08-07 19:50:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000413s ]
[2025-08-07 19:50:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567416 [ RunTime:0.000272s ]
[2025-08-07 19:50:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000518s ]
[2025-08-07 19:50:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000240s ]
[2025-08-07 19:50:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000328s ]
[2025-08-07 19:50:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000606s ]
[2025-08-07 19:50:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000234s ]
[2025-08-07 19:50:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000370s ]
[2025-08-07 19:50:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000344s ]
[2025-08-07 19:50:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001705s ]
[2025-08-07 19:50:16][sql] CONNECT:[ UseTime:0.020523s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:50:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000727s ]
[2025-08-07 19:50:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000393s ]
[2025-08-07 19:50:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567416 [ RunTime:0.000216s ]
[2025-08-07 19:50:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000521s ]
[2025-08-07 19:50:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000251s ]
[2025-08-07 19:50:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000334s ]
[2025-08-07 19:50:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000551s ]
[2025-08-07 19:50:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000232s ]
[2025-08-07 19:50:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000352s ]
[2025-08-07 19:50:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000355s ]
[2025-08-07 19:50:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001793s ]
[2025-08-07 19:51:16][sql] CONNECT:[ UseTime:0.021566s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:51:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000812s ]
[2025-08-07 19:51:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000397s ]
[2025-08-07 19:51:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567476 [ RunTime:0.000211s ]
[2025-08-07 19:51:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000549s ]
[2025-08-07 19:51:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000249s ]
[2025-08-07 19:51:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000336s ]
[2025-08-07 19:51:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000566s ]
[2025-08-07 19:51:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000228s ]
[2025-08-07 19:51:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000339s ]
[2025-08-07 19:51:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000313s ]
[2025-08-07 19:51:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001757s ]
[2025-08-07 19:51:16][sql] CONNECT:[ UseTime:0.020867s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:51:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000872s ]
[2025-08-07 19:51:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000403s ]
[2025-08-07 19:51:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567476 [ RunTime:0.000220s ]
[2025-08-07 19:51:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000642s ]
[2025-08-07 19:51:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000273s ]
[2025-08-07 19:51:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000328s ]
[2025-08-07 19:51:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000581s ]
[2025-08-07 19:51:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000230s ]
[2025-08-07 19:51:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000344s ]
[2025-08-07 19:51:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000509s ]
[2025-08-07 19:51:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001885s ]
[2025-08-07 19:52:16][sql] CONNECT:[ UseTime:0.021395s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:52:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000753s ]
[2025-08-07 19:52:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000399s ]
[2025-08-07 19:52:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567536 [ RunTime:0.000218s ]
[2025-08-07 19:52:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000528s ]
[2025-08-07 19:52:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000244s ]
[2025-08-07 19:52:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000381s ]
[2025-08-07 19:52:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000552s ]
[2025-08-07 19:52:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000241s ]
[2025-08-07 19:52:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000339s ]
[2025-08-07 19:52:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000317s ]
[2025-08-07 19:52:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001719s ]
[2025-08-07 19:52:16][sql] CONNECT:[ UseTime:0.018305s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:52:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000686s ]
[2025-08-07 19:52:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000391s ]
[2025-08-07 19:52:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567536 [ RunTime:0.000213s ]
[2025-08-07 19:52:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000527s ]
[2025-08-07 19:52:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000247s ]
[2025-08-07 19:52:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000334s ]
[2025-08-07 19:52:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000570s ]
[2025-08-07 19:52:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000228s ]
[2025-08-07 19:52:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000341s ]
[2025-08-07 19:52:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000313s ]
[2025-08-07 19:52:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001843s ]
[2025-08-07 19:53:16][sql] CONNECT:[ UseTime:0.018269s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:53:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000707s ]
[2025-08-07 19:53:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000402s ]
[2025-08-07 19:53:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567596 [ RunTime:0.000220s ]
[2025-08-07 19:53:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000529s ]
[2025-08-07 19:53:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000259s ]
[2025-08-07 19:53:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000377s ]
[2025-08-07 19:53:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000581s ]
[2025-08-07 19:53:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000230s ]
[2025-08-07 19:53:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000533s ]
[2025-08-07 19:53:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000514s ]
[2025-08-07 19:53:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002108s ]
[2025-08-07 19:53:16][sql] CONNECT:[ UseTime:0.018734s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:53:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000682s ]
[2025-08-07 19:53:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000399s ]
[2025-08-07 19:53:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567596 [ RunTime:0.000230s ]
[2025-08-07 19:53:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000543s ]
[2025-08-07 19:53:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000252s ]
[2025-08-07 19:53:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000326s ]
[2025-08-07 19:53:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000573s ]
[2025-08-07 19:53:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000230s ]
[2025-08-07 19:53:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000340s ]
[2025-08-07 19:53:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000344s ]
[2025-08-07 19:53:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001751s ]
[2025-08-07 19:54:16][sql] CONNECT:[ UseTime:0.020545s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:54:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000735s ]
[2025-08-07 19:54:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000400s ]
[2025-08-07 19:54:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567656 [ RunTime:0.000219s ]
[2025-08-07 19:54:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000540s ]
[2025-08-07 19:54:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000291s ]
[2025-08-07 19:54:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000331s ]
[2025-08-07 19:54:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000582s ]
[2025-08-07 19:54:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000244s ]
[2025-08-07 19:54:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000473s ]
[2025-08-07 19:54:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000549s ]
[2025-08-07 19:54:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002046s ]
[2025-08-07 19:54:16][sql] CONNECT:[ UseTime:0.020530s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:54:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000727s ]
[2025-08-07 19:54:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000393s ]
[2025-08-07 19:54:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567656 [ RunTime:0.000216s ]
[2025-08-07 19:54:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000533s ]
[2025-08-07 19:54:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000239s ]
[2025-08-07 19:54:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000326s ]
[2025-08-07 19:54:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000538s ]
[2025-08-07 19:54:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000236s ]
[2025-08-07 19:54:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000347s ]
[2025-08-07 19:54:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000325s ]
[2025-08-07 19:54:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001960s ]
[2025-08-07 19:55:16][sql] CONNECT:[ UseTime:0.019195s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:55:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000717s ]
[2025-08-07 19:55:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000394s ]
[2025-08-07 19:55:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567716 [ RunTime:0.000216s ]
[2025-08-07 19:55:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000557s ]
[2025-08-07 19:55:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000266s ]
[2025-08-07 19:55:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000374s ]
[2025-08-07 19:55:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000597s ]
[2025-08-07 19:55:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000231s ]
[2025-08-07 19:55:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000356s ]
[2025-08-07 19:55:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000322s ]
[2025-08-07 19:55:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001692s ]
[2025-08-07 19:55:16][sql] CONNECT:[ UseTime:0.022074s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:55:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000692s ]
[2025-08-07 19:55:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000406s ]
[2025-08-07 19:55:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567716 [ RunTime:0.000220s ]
[2025-08-07 19:55:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000498s ]
[2025-08-07 19:55:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000245s ]
[2025-08-07 19:55:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000335s ]
[2025-08-07 19:55:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000604s ]
[2025-08-07 19:55:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000225s ]
[2025-08-07 19:55:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000340s ]
[2025-08-07 19:55:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000321s ]
[2025-08-07 19:55:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001730s ]
[2025-08-07 19:56:16][sql] CONNECT:[ UseTime:0.020175s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:56:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000698s ]
[2025-08-07 19:56:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000401s ]
[2025-08-07 19:56:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567776 [ RunTime:0.000222s ]
[2025-08-07 19:56:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000578s ]
[2025-08-07 19:56:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000250s ]
[2025-08-07 19:56:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000330s ]
[2025-08-07 19:56:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000585s ]
[2025-08-07 19:56:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000229s ]
[2025-08-07 19:56:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000348s ]
[2025-08-07 19:56:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000368s ]
[2025-08-07 19:56:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001994s ]
[2025-08-07 19:56:16][sql] CONNECT:[ UseTime:0.018901s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:56:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000746s ]
[2025-08-07 19:56:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000397s ]
[2025-08-07 19:56:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567776 [ RunTime:0.000326s ]
[2025-08-07 19:56:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000689s ]
[2025-08-07 19:56:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000244s ]
[2025-08-07 19:56:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000329s ]
[2025-08-07 19:56:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000616s ]
[2025-08-07 19:56:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000376s ]
[2025-08-07 19:56:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000413s ]
[2025-08-07 19:56:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000327s ]
[2025-08-07 19:56:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001906s ]
[2025-08-07 19:57:16][sql] CONNECT:[ UseTime:0.019792s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:57:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000794s ]
[2025-08-07 19:57:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000406s ]
[2025-08-07 19:57:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567836 [ RunTime:0.000350s ]
[2025-08-07 19:57:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000601s ]
[2025-08-07 19:57:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000307s ]
[2025-08-07 19:57:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000334s ]
[2025-08-07 19:57:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000577s ]
[2025-08-07 19:57:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000228s ]
[2025-08-07 19:57:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000342s ]
[2025-08-07 19:57:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000312s ]
[2025-08-07 19:57:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001931s ]
[2025-08-07 19:57:16][sql] CONNECT:[ UseTime:0.020075s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:57:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000735s ]
[2025-08-07 19:57:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000392s ]
[2025-08-07 19:57:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567836 [ RunTime:0.000218s ]
[2025-08-07 19:57:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000545s ]
[2025-08-07 19:57:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000241s ]
[2025-08-07 19:57:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000330s ]
[2025-08-07 19:57:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000574s ]
[2025-08-07 19:57:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000224s ]
[2025-08-07 19:57:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000334s ]
[2025-08-07 19:57:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000305s ]
[2025-08-07 19:57:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002237s ]
[2025-08-07 19:58:16][sql] CONNECT:[ UseTime:0.020510s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:58:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000733s ]
[2025-08-07 19:58:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000395s ]
[2025-08-07 19:58:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567896 [ RunTime:0.000218s ]
[2025-08-07 19:58:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000517s ]
[2025-08-07 19:58:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000251s ]
[2025-08-07 19:58:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000331s ]
[2025-08-07 19:58:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000850s ]
[2025-08-07 19:58:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000236s ]
[2025-08-07 19:58:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000351s ]
[2025-08-07 19:58:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000318s ]
[2025-08-07 19:58:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001855s ]
[2025-08-07 19:58:16][sql] CONNECT:[ UseTime:0.020005s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:58:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000740s ]
[2025-08-07 19:58:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000487s ]
[2025-08-07 19:58:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567896 [ RunTime:0.000216s ]
[2025-08-07 19:58:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000531s ]
[2025-08-07 19:58:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000254s ]
[2025-08-07 19:58:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000334s ]
[2025-08-07 19:58:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000569s ]
[2025-08-07 19:58:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000228s ]
[2025-08-07 19:58:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000340s ]
[2025-08-07 19:58:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000318s ]
[2025-08-07 19:58:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001685s ]
[2025-08-07 19:59:16][sql] CONNECT:[ UseTime:0.019547s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:59:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000765s ]
[2025-08-07 19:59:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000402s ]
[2025-08-07 19:59:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567956 [ RunTime:0.000223s ]
[2025-08-07 19:59:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000858s ]
[2025-08-07 19:59:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000376s ]
[2025-08-07 19:59:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000467s ]
[2025-08-07 19:59:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000900s ]
[2025-08-07 19:59:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000363s ]
[2025-08-07 19:59:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000483s ]
[2025-08-07 19:59:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000451s ]
[2025-08-07 19:59:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.003022s ]
[2025-08-07 19:59:16][sql] CONNECT:[ UseTime:0.019821s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 19:59:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000742s ]
[2025-08-07 19:59:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000435s ]
[2025-08-07 19:59:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754567956 [ RunTime:0.000233s ]
[2025-08-07 19:59:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000526s ]
[2025-08-07 19:59:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000249s ]
[2025-08-07 19:59:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000336s ]
[2025-08-07 19:59:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000634s ]
[2025-08-07 19:59:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000230s ]
[2025-08-07 19:59:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000341s ]
[2025-08-07 19:59:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000313s ]
[2025-08-07 19:59:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001740s ]
[2025-08-07 20:00:16][sql] CONNECT:[ UseTime:0.020810s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:00:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000874s ]
[2025-08-07 20:00:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000488s ]
[2025-08-07 20:00:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568016 [ RunTime:0.000236s ]
[2025-08-07 20:00:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000497s ]
[2025-08-07 20:00:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000276s ]
[2025-08-07 20:00:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000363s ]
[2025-08-07 20:00:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000597s ]
[2025-08-07 20:00:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000234s ]
[2025-08-07 20:00:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000661s ]
[2025-08-07 20:00:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000611s ]
[2025-08-07 20:00:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002401s ]
[2025-08-07 20:00:16][sql] CONNECT:[ UseTime:0.019089s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:00:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000738s ]
[2025-08-07 20:00:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000388s ]
[2025-08-07 20:00:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568016 [ RunTime:0.000236s ]
[2025-08-07 20:00:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000664s ]
[2025-08-07 20:00:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000246s ]
[2025-08-07 20:00:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000339s ]
[2025-08-07 20:00:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000540s ]
[2025-08-07 20:00:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000231s ]
[2025-08-07 20:00:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000345s ]
[2025-08-07 20:00:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000573s ]
[2025-08-07 20:00:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002119s ]
[2025-08-07 20:01:16][sql] CONNECT:[ UseTime:0.020465s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:01:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000791s ]
[2025-08-07 20:01:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000394s ]
[2025-08-07 20:01:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568076 [ RunTime:0.000220s ]
[2025-08-07 20:01:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000521s ]
[2025-08-07 20:01:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000252s ]
[2025-08-07 20:01:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000333s ]
[2025-08-07 20:01:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000581s ]
[2025-08-07 20:01:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000231s ]
[2025-08-07 20:01:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000343s ]
[2025-08-07 20:01:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000325s ]
[2025-08-07 20:01:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002661s ]
[2025-08-07 20:01:16][sql] CONNECT:[ UseTime:0.018957s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:01:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000764s ]
[2025-08-07 20:01:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000397s ]
[2025-08-07 20:01:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568076 [ RunTime:0.000216s ]
[2025-08-07 20:01:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000565s ]
[2025-08-07 20:01:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000253s ]
[2025-08-07 20:01:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000426s ]
[2025-08-07 20:01:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000610s ]
[2025-08-07 20:01:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000232s ]
[2025-08-07 20:01:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000340s ]
[2025-08-07 20:01:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000317s ]
[2025-08-07 20:01:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001873s ]
[2025-08-07 20:02:16][sql] CONNECT:[ UseTime:0.018665s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:02:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000732s ]
[2025-08-07 20:02:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000400s ]
[2025-08-07 20:02:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568136 [ RunTime:0.000219s ]
[2025-08-07 20:02:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000549s ]
[2025-08-07 20:02:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000250s ]
[2025-08-07 20:02:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000381s ]
[2025-08-07 20:02:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000591s ]
[2025-08-07 20:02:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000438s ]
[2025-08-07 20:02:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000462s ]
[2025-08-07 20:02:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000453s ]
[2025-08-07 20:02:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001881s ]
[2025-08-07 20:02:16][sql] CONNECT:[ UseTime:0.019898s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:02:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000786s ]
[2025-08-07 20:02:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000402s ]
[2025-08-07 20:02:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568136 [ RunTime:0.000215s ]
[2025-08-07 20:02:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000548s ]
[2025-08-07 20:02:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000272s ]
[2025-08-07 20:02:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000468s ]
[2025-08-07 20:02:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000651s ]
[2025-08-07 20:02:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000252s ]
[2025-08-07 20:02:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000533s ]
[2025-08-07 20:02:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000338s ]
[2025-08-07 20:02:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001804s ]
[2025-08-07 20:03:16][sql] CONNECT:[ UseTime:0.019538s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:03:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000759s ]
[2025-08-07 20:03:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000404s ]
[2025-08-07 20:03:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568196 [ RunTime:0.000238s ]
[2025-08-07 20:03:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000532s ]
[2025-08-07 20:03:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000248s ]
[2025-08-07 20:03:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000333s ]
[2025-08-07 20:03:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000620s ]
[2025-08-07 20:03:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000234s ]
[2025-08-07 20:03:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000363s ]
[2025-08-07 20:03:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000336s ]
[2025-08-07 20:03:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001724s ]
[2025-08-07 20:03:16][sql] CONNECT:[ UseTime:0.020771s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:03:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000743s ]
[2025-08-07 20:03:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000397s ]
[2025-08-07 20:03:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568196 [ RunTime:0.000218s ]
[2025-08-07 20:03:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000492s ]
[2025-08-07 20:03:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000252s ]
[2025-08-07 20:03:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000341s ]
[2025-08-07 20:03:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000582s ]
[2025-08-07 20:03:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000328s ]
[2025-08-07 20:03:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000354s ]
[2025-08-07 20:03:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000330s ]
[2025-08-07 20:03:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001847s ]
[2025-08-07 20:04:16][sql] CONNECT:[ UseTime:0.021277s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:04:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000728s ]
[2025-08-07 20:04:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000436s ]
[2025-08-07 20:04:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568256 [ RunTime:0.000239s ]
[2025-08-07 20:04:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000521s ]
[2025-08-07 20:04:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000246s ]
[2025-08-07 20:04:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000334s ]
[2025-08-07 20:04:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000597s ]
[2025-08-07 20:04:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000236s ]
[2025-08-07 20:04:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000678s ]
[2025-08-07 20:04:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000483s ]
[2025-08-07 20:04:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002031s ]
[2025-08-07 20:04:16][sql] CONNECT:[ UseTime:0.017270s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:04:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000844s ]
[2025-08-07 20:04:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000398s ]
[2025-08-07 20:04:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568256 [ RunTime:0.000216s ]
[2025-08-07 20:04:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000512s ]
[2025-08-07 20:04:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000251s ]
[2025-08-07 20:04:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000407s ]
[2025-08-07 20:04:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000609s ]
[2025-08-07 20:04:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000243s ]
[2025-08-07 20:04:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000357s ]
[2025-08-07 20:04:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000321s ]
[2025-08-07 20:04:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001775s ]
[2025-08-07 20:05:16][sql] CONNECT:[ UseTime:0.018430s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:05:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000766s ]
[2025-08-07 20:05:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000398s ]
[2025-08-07 20:05:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568316 [ RunTime:0.000216s ]
[2025-08-07 20:05:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000519s ]
[2025-08-07 20:05:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000263s ]
[2025-08-07 20:05:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000338s ]
[2025-08-07 20:05:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000768s ]
[2025-08-07 20:05:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000267s ]
[2025-08-07 20:05:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000351s ]
[2025-08-07 20:05:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000313s ]
[2025-08-07 20:05:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001759s ]
[2025-08-07 20:05:16][sql] CONNECT:[ UseTime:0.020412s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:05:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000754s ]
[2025-08-07 20:05:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000563s ]
[2025-08-07 20:05:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568316 [ RunTime:0.000230s ]
[2025-08-07 20:05:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000536s ]
[2025-08-07 20:05:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000249s ]
[2025-08-07 20:05:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000336s ]
[2025-08-07 20:05:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000677s ]
[2025-08-07 20:05:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000263s ]
[2025-08-07 20:05:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000369s ]
[2025-08-07 20:05:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000331s ]
[2025-08-07 20:05:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002065s ]
[2025-08-07 20:06:16][sql] CONNECT:[ UseTime:0.021761s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:06:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000824s ]
[2025-08-07 20:06:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000459s ]
[2025-08-07 20:06:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568376 [ RunTime:0.000240s ]
[2025-08-07 20:06:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000524s ]
[2025-08-07 20:06:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000247s ]
[2025-08-07 20:06:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000333s ]
[2025-08-07 20:06:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000576s ]
[2025-08-07 20:06:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000225s ]
[2025-08-07 20:06:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000341s ]
[2025-08-07 20:06:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000333s ]
[2025-08-07 20:06:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001691s ]
[2025-08-07 20:06:16][sql] CONNECT:[ UseTime:0.021213s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:06:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000810s ]
[2025-08-07 20:06:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000396s ]
[2025-08-07 20:06:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568376 [ RunTime:0.000219s ]
[2025-08-07 20:06:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000539s ]
[2025-08-07 20:06:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000247s ]
[2025-08-07 20:06:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000329s ]
[2025-08-07 20:06:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000647s ]
[2025-08-07 20:06:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000229s ]
[2025-08-07 20:06:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000346s ]
[2025-08-07 20:06:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000486s ]
[2025-08-07 20:06:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001783s ]
[2025-08-07 20:07:16][sql] CONNECT:[ UseTime:0.019916s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:07:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000691s ]
[2025-08-07 20:07:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000537s ]
[2025-08-07 20:07:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568436 [ RunTime:0.000237s ]
[2025-08-07 20:07:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000506s ]
[2025-08-07 20:07:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000256s ]
[2025-08-07 20:07:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000327s ]
[2025-08-07 20:07:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000700s ]
[2025-08-07 20:07:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000235s ]
[2025-08-07 20:07:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000408s ]
[2025-08-07 20:07:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000324s ]
[2025-08-07 20:07:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001691s ]
[2025-08-07 20:07:16][sql] CONNECT:[ UseTime:0.019970s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:07:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000727s ]
[2025-08-07 20:07:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000431s ]
[2025-08-07 20:07:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568436 [ RunTime:0.000234s ]
[2025-08-07 20:07:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000682s ]
[2025-08-07 20:07:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000252s ]
[2025-08-07 20:07:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000341s ]
[2025-08-07 20:07:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000618s ]
[2025-08-07 20:07:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000232s ]
[2025-08-07 20:07:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000481s ]
[2025-08-07 20:07:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000326s ]
[2025-08-07 20:07:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001745s ]
[2025-08-07 20:08:16][sql] CONNECT:[ UseTime:0.019563s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:08:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000887s ]
[2025-08-07 20:08:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000408s ]
[2025-08-07 20:08:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568496 [ RunTime:0.000267s ]
[2025-08-07 20:08:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000537s ]
[2025-08-07 20:08:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000286s ]
[2025-08-07 20:08:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000338s ]
[2025-08-07 20:08:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000620s ]
[2025-08-07 20:08:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000240s ]
[2025-08-07 20:08:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000346s ]
[2025-08-07 20:08:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000384s ]
[2025-08-07 20:08:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002164s ]
[2025-08-07 20:08:16][sql] CONNECT:[ UseTime:0.019979s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:08:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000738s ]
[2025-08-07 20:08:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000415s ]
[2025-08-07 20:08:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568496 [ RunTime:0.000217s ]
[2025-08-07 20:08:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000641s ]
[2025-08-07 20:08:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000240s ]
[2025-08-07 20:08:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000334s ]
[2025-08-07 20:08:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000713s ]
[2025-08-07 20:08:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000239s ]
[2025-08-07 20:08:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000356s ]
[2025-08-07 20:08:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000317s ]
[2025-08-07 20:08:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001696s ]
[2025-08-07 20:09:16][sql] CONNECT:[ UseTime:0.018906s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:09:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000727s ]
[2025-08-07 20:09:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000405s ]
[2025-08-07 20:09:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568556 [ RunTime:0.000225s ]
[2025-08-07 20:09:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000500s ]
[2025-08-07 20:09:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000244s ]
[2025-08-07 20:09:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000460s ]
[2025-08-07 20:09:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000589s ]
[2025-08-07 20:09:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000231s ]
[2025-08-07 20:09:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000435s ]
[2025-08-07 20:09:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000324s ]
[2025-08-07 20:09:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001813s ]
[2025-08-07 20:09:16][sql] CONNECT:[ UseTime:0.021160s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:09:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000709s ]
[2025-08-07 20:09:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000398s ]
[2025-08-07 20:09:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568556 [ RunTime:0.000220s ]
[2025-08-07 20:09:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000507s ]
[2025-08-07 20:09:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000249s ]
[2025-08-07 20:09:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000342s ]
[2025-08-07 20:09:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000583s ]
[2025-08-07 20:09:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000371s ]
[2025-08-07 20:09:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000354s ]
[2025-08-07 20:09:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000393s ]
[2025-08-07 20:09:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001797s ]
[2025-08-07 20:10:16][sql] CONNECT:[ UseTime:0.020863s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:10:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000684s ]
[2025-08-07 20:10:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000482s ]
[2025-08-07 20:10:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568616 [ RunTime:0.000217s ]
[2025-08-07 20:10:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000514s ]
[2025-08-07 20:10:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000250s ]
[2025-08-07 20:10:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000338s ]
[2025-08-07 20:10:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000579s ]
[2025-08-07 20:10:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000228s ]
[2025-08-07 20:10:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000341s ]
[2025-08-07 20:10:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000316s ]
[2025-08-07 20:10:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001876s ]
[2025-08-07 20:10:16][sql] CONNECT:[ UseTime:0.018838s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:10:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000711s ]
[2025-08-07 20:10:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000722s ]
[2025-08-07 20:10:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568616 [ RunTime:0.000233s ]
[2025-08-07 20:10:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000535s ]
[2025-08-07 20:10:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000240s ]
[2025-08-07 20:10:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000357s ]
[2025-08-07 20:10:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000535s ]
[2025-08-07 20:10:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000224s ]
[2025-08-07 20:10:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000341s ]
[2025-08-07 20:10:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000313s ]
[2025-08-07 20:10:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001714s ]
[2025-08-07 20:11:16][sql] CONNECT:[ UseTime:0.021445s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:11:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.001049s ]
[2025-08-07 20:11:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000408s ]
[2025-08-07 20:11:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568676 [ RunTime:0.000227s ]
[2025-08-07 20:11:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000525s ]
[2025-08-07 20:11:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000258s ]
[2025-08-07 20:11:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000340s ]
[2025-08-07 20:11:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000651s ]
[2025-08-07 20:11:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000233s ]
[2025-08-07 20:11:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000345s ]
[2025-08-07 20:11:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000317s ]
[2025-08-07 20:11:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001840s ]
[2025-08-07 20:11:16][sql] CONNECT:[ UseTime:0.020646s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:11:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000771s ]
[2025-08-07 20:11:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000410s ]
[2025-08-07 20:11:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568676 [ RunTime:0.000217s ]
[2025-08-07 20:11:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000540s ]
[2025-08-07 20:11:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000246s ]
[2025-08-07 20:11:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000337s ]
[2025-08-07 20:11:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000729s ]
[2025-08-07 20:11:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000253s ]
[2025-08-07 20:11:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000353s ]
[2025-08-07 20:11:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000320s ]
[2025-08-07 20:11:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001791s ]
[2025-08-07 20:12:16][sql] CONNECT:[ UseTime:0.018582s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:12:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000684s ]
[2025-08-07 20:12:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000401s ]
[2025-08-07 20:12:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568736 [ RunTime:0.000218s ]
[2025-08-07 20:12:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000730s ]
[2025-08-07 20:12:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000264s ]
[2025-08-07 20:12:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000331s ]
[2025-08-07 20:12:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000625s ]
[2025-08-07 20:12:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000433s ]
[2025-08-07 20:12:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000629s ]
[2025-08-07 20:12:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000454s ]
[2025-08-07 20:12:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.002361s ]
[2025-08-07 20:12:16][sql] CONNECT:[ UseTime:0.017571s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-08-07 20:12:16][sql] show tables like 'ea_system_log_202508' [ RunTime:0.000735s ]
[2025-08-07 20:12:16][sql] SHOW FULL COLUMNS FROM `ea_system_log_202508` [ RunTime:0.000535s ]
[2025-08-07 20:12:16][sql] INSERT INTO `ea_system_log_202508` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '127.0.0.1' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' , `create_time` = 1754568736 [ RunTime:0.000244s ]
[2025-08-07 20:12:16][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000542s ]
[2025-08-07 20:12:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000242s ]
[2025-08-07 20:12:16][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000333s ]
[2025-08-07 20:12:16][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000665s ]
[2025-08-07 20:12:16][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000307s ]
[2025-08-07 20:12:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000351s ]
[2025-08-07 20:12:16][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000329s ]
[2025-08-07 20:12:16][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001730s ]
