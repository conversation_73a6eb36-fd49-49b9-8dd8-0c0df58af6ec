[2025-07-20 23:51:34][sql] CONNECT:[ UseTime:0.000481s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-20 23:51:34][sql] show tables like 'ea_system_log_202507' [ RunTime:0.000436s ]
[2025-07-20 23:51:34][sql] SHOW FULL COLUMNS FROM `ea_system_log_202507` [ RunTime:0.000295s ]
[2025-07-20 23:51:34][sql] INSERT INTO `ea_system_log_202507` SET `admin_id` = 6112 , `url` = '/Common/checkSession' , `method` = 'post' , `ip` = '*************' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.2.5.446' , `create_time` = 1753026694 [ RunTime:0.000111s ]
[2025-07-20 23:51:34][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000537s ]
[2025-07-20 23:51:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000258s ]
[2025-07-20 23:51:34][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000232s ]
[2025-07-20 23:51:34][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000398s ]
[2025-07-20 23:51:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000154s ]
[2025-07-20 23:51:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000208s ]
[2025-07-20 23:51:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000154s ]
[2025-07-20 23:51:34][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001457s ]
[2025-07-20 23:51:34][sql] CONNECT:[ UseTime:0.000498s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-20 23:51:34][sql] show tables like 'ea_system_log_202507' [ RunTime:0.000296s ]
[2025-07-20 23:51:34][sql] SHOW FULL COLUMNS FROM `ea_system_log_202507` [ RunTime:0.000334s ]
[2025-07-20 23:51:34][sql] INSERT INTO `ea_system_log_202507` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '*************' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.2.5.446' , `create_time` = 1753026694 [ RunTime:0.000268s ]
[2025-07-20 23:51:34][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000293s ]
[2025-07-20 23:51:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000123s ]
[2025-07-20 23:51:34][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000164s ]
[2025-07-20 23:51:34][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000389s ]
[2025-07-20 23:51:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000145s ]
[2025-07-20 23:51:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000168s ]
[2025-07-20 23:51:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000151s ]
[2025-07-20 23:51:34][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001265s ]
[2025-07-20 23:52:34][sql] CONNECT:[ UseTime:0.000244s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-20 23:52:34][sql] show tables like 'ea_system_log_202507' [ RunTime:0.000279s ]
[2025-07-20 23:52:34][sql] SHOW FULL COLUMNS FROM `ea_system_log_202507` [ RunTime:0.000323s ]
[2025-07-20 23:52:34][sql] INSERT INTO `ea_system_log_202507` SET `admin_id` = 6112 , `url` = '/Common/checkSession' , `method` = 'post' , `ip` = '*************' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.2.5.446' , `create_time` = 1753026754 [ RunTime:0.000220s ]
[2025-07-20 23:52:34][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000256s ]
[2025-07-20 23:52:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000248s ]
[2025-07-20 23:52:34][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000416s ]
[2025-07-20 23:52:34][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000596s ]
[2025-07-20 23:52:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000215s ]
[2025-07-20 23:52:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000241s ]
[2025-07-20 23:52:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000184s ]
[2025-07-20 23:52:34][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001610s ]
[2025-07-20 23:52:34][sql] CONNECT:[ UseTime:0.000263s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-20 23:52:34][sql] show tables like 'ea_system_log_202507' [ RunTime:0.000253s ]
[2025-07-20 23:52:34][sql] SHOW FULL COLUMNS FROM `ea_system_log_202507` [ RunTime:0.000379s ]
[2025-07-20 23:52:34][sql] INSERT INTO `ea_system_log_202507` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '*************' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.2.5.446' , `create_time` = 1753026754 [ RunTime:0.000243s ]
[2025-07-20 23:52:34][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000475s ]
[2025-07-20 23:52:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000145s ]
[2025-07-20 23:52:34][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000191s ]
[2025-07-20 23:52:34][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000442s ]
[2025-07-20 23:52:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000164s ]
[2025-07-20 23:52:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000246s ]
[2025-07-20 23:52:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000136s ]
[2025-07-20 23:52:34][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001233s ]
[2025-07-20 23:53:34][sql] CONNECT:[ UseTime:0.000410s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-20 23:53:34][sql] show tables like 'ea_system_log_202507' [ RunTime:0.000380s ]
[2025-07-20 23:53:34][sql] SHOW FULL COLUMNS FROM `ea_system_log_202507` [ RunTime:0.000235s ]
[2025-07-20 23:53:34][sql] INSERT INTO `ea_system_log_202507` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '*************' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.2.5.446' , `create_time` = 1753026814 [ RunTime:0.000115s ]
[2025-07-20 23:53:34][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000413s ]
[2025-07-20 23:53:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000175s ]
[2025-07-20 23:53:34][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000153s ]
[2025-07-20 23:53:34][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000382s ]
[2025-07-20 23:53:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000161s ]
[2025-07-20 23:53:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000218s ]
[2025-07-20 23:53:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000169s ]
[2025-07-20 23:53:34][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001646s ]
[2025-07-20 23:53:34][sql] CONNECT:[ UseTime:0.000368s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-20 23:53:34][sql] show tables like 'ea_system_log_202507' [ RunTime:0.000287s ]
[2025-07-20 23:53:34][sql] SHOW FULL COLUMNS FROM `ea_system_log_202507` [ RunTime:0.000317s ]
[2025-07-20 23:53:34][sql] INSERT INTO `ea_system_log_202507` SET `admin_id` = 6112 , `url` = '/Common/checkSession' , `method` = 'post' , `ip` = '*************' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.2.5.446' , `create_time` = 1753026814 [ RunTime:0.000152s ]
[2025-07-20 23:53:34][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000315s ]
[2025-07-20 23:53:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000186s ]
[2025-07-20 23:53:34][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000256s ]
[2025-07-20 23:53:34][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000492s ]
[2025-07-20 23:53:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000266s ]
[2025-07-20 23:53:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000420s ]
[2025-07-20 23:53:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000153s ]
[2025-07-20 23:53:34][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001460s ]
[2025-07-20 23:54:34][sql] CONNECT:[ UseTime:0.000486s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-20 23:54:34][sql] show tables like 'ea_system_log_202507' [ RunTime:0.000472s ]
[2025-07-20 23:54:34][sql] SHOW FULL COLUMNS FROM `ea_system_log_202507` [ RunTime:0.000356s ]
[2025-07-20 23:54:34][sql] INSERT INTO `ea_system_log_202507` SET `admin_id` = 6112 , `url` = '/Common/checkSession' , `method` = 'post' , `ip` = '*************' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.2.5.446' , `create_time` = 1753026874 [ RunTime:0.000164s ]
[2025-07-20 23:54:34][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000246s ]
[2025-07-20 23:54:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000127s ]
[2025-07-20 23:54:34][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000151s ]
[2025-07-20 23:54:34][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000387s ]
[2025-07-20 23:54:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000145s ]
[2025-07-20 23:54:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000165s ]
[2025-07-20 23:54:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000143s ]
[2025-07-20 23:54:34][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001347s ]
[2025-07-20 23:54:34][sql] CONNECT:[ UseTime:0.000472s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-20 23:54:34][sql] show tables like 'ea_system_log_202507' [ RunTime:0.000453s ]
[2025-07-20 23:54:34][sql] SHOW FULL COLUMNS FROM `ea_system_log_202507` [ RunTime:0.000409s ]
[2025-07-20 23:54:34][sql] INSERT INTO `ea_system_log_202507` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '*************' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.2.5.446' , `create_time` = 1753026874 [ RunTime:0.000160s ]
[2025-07-20 23:54:34][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000258s ]
[2025-07-20 23:54:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000109s ]
[2025-07-20 23:54:34][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000149s ]
[2025-07-20 23:54:34][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000344s ]
[2025-07-20 23:54:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000135s ]
[2025-07-20 23:54:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000153s ]
[2025-07-20 23:54:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000236s ]
[2025-07-20 23:54:34][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001277s ]
[2025-07-20 23:55:34][sql] CONNECT:[ UseTime:0.000567s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-20 23:55:34][sql] show tables like 'ea_system_log_202507' [ RunTime:0.000286s ]
[2025-07-20 23:55:34][sql] SHOW FULL COLUMNS FROM `ea_system_log_202507` [ RunTime:0.000176s ]
[2025-07-20 23:55:34][sql] INSERT INTO `ea_system_log_202507` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '*************' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.2.5.446' , `create_time` = 1753026934 [ RunTime:0.000111s ]
[2025-07-20 23:55:34][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000354s ]
[2025-07-20 23:55:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000146s ]
[2025-07-20 23:55:34][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000177s ]
[2025-07-20 23:55:34][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000516s ]
[2025-07-20 23:55:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000281s ]
[2025-07-20 23:55:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000255s ]
[2025-07-20 23:55:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000200s ]
[2025-07-20 23:55:34][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001787s ]
[2025-07-20 23:55:34][sql] CONNECT:[ UseTime:0.000554s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-20 23:55:34][sql] show tables like 'ea_system_log_202507' [ RunTime:0.000330s ]
[2025-07-20 23:55:34][sql] SHOW FULL COLUMNS FROM `ea_system_log_202507` [ RunTime:0.000208s ]
[2025-07-20 23:55:34][sql] INSERT INTO `ea_system_log_202507` SET `admin_id` = 6112 , `url` = '/Common/checkSession' , `method` = 'post' , `ip` = '*************' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.2.5.446' , `create_time` = 1753026934 [ RunTime:0.000117s ]
[2025-07-20 23:55:34][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000506s ]
[2025-07-20 23:55:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000308s ]
[2025-07-20 23:55:34][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000285s ]
[2025-07-20 23:55:34][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000549s ]
[2025-07-20 23:55:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000231s ]
[2025-07-20 23:55:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000228s ]
[2025-07-20 23:55:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000197s ]
[2025-07-20 23:55:34][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001361s ]
[2025-07-20 23:56:34][sql] CONNECT:[ UseTime:0.000315s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-20 23:56:34][sql] show tables like 'ea_system_log_202507' [ RunTime:0.000318s ]
[2025-07-20 23:56:34][sql] SHOW FULL COLUMNS FROM `ea_system_log_202507` [ RunTime:0.000200s ]
[2025-07-20 23:56:34][sql] INSERT INTO `ea_system_log_202507` SET `admin_id` = 6112 , `url` = '/Common/checkSession' , `method` = 'post' , `ip` = '*************' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.2.5.446' , `create_time` = 1753026994 [ RunTime:0.000114s ]
[2025-07-20 23:56:34][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000263s ]
[2025-07-20 23:56:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000134s ]
[2025-07-20 23:56:34][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000236s ]
[2025-07-20 23:56:34][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000352s ]
[2025-07-20 23:56:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000140s ]
[2025-07-20 23:56:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000217s ]
[2025-07-20 23:56:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000157s ]
[2025-07-20 23:56:34][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001425s ]
[2025-07-20 23:56:34][sql] CONNECT:[ UseTime:0.000272s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-20 23:56:34][sql] show tables like 'ea_system_log_202507' [ RunTime:0.000492s ]
[2025-07-20 23:56:34][sql] SHOW FULL COLUMNS FROM `ea_system_log_202507` [ RunTime:0.000349s ]
[2025-07-20 23:56:34][sql] INSERT INTO `ea_system_log_202507` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '*************' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.2.5.446' , `create_time` = 1753026994 [ RunTime:0.000096s ]
[2025-07-20 23:56:34][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000394s ]
[2025-07-20 23:56:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000274s ]
[2025-07-20 23:56:34][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000258s ]
[2025-07-20 23:56:34][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000348s ]
[2025-07-20 23:56:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000141s ]
[2025-07-20 23:56:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000223s ]
[2025-07-20 23:56:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000153s ]
[2025-07-20 23:56:34][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001293s ]
[2025-07-20 23:57:34][sql] CONNECT:[ UseTime:0.000455s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-20 23:57:34][sql] show tables like 'ea_system_log_202507' [ RunTime:0.000375s ]
[2025-07-20 23:57:34][sql] SHOW FULL COLUMNS FROM `ea_system_log_202507` [ RunTime:0.000292s ]
[2025-07-20 23:57:34][sql] INSERT INTO `ea_system_log_202507` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '*************' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.2.5.446' , `create_time` = 1753027054 [ RunTime:0.000179s ]
[2025-07-20 23:57:34][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000412s ]
[2025-07-20 23:57:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000251s ]
[2025-07-20 23:57:34][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000322s ]
[2025-07-20 23:57:34][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000446s ]
[2025-07-20 23:57:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000153s ]
[2025-07-20 23:57:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000206s ]
[2025-07-20 23:57:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000149s ]
[2025-07-20 23:57:34][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001469s ]
[2025-07-20 23:57:34][sql] CONNECT:[ UseTime:0.000480s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-20 23:57:34][sql] show tables like 'ea_system_log_202507' [ RunTime:0.000427s ]
[2025-07-20 23:57:34][sql] SHOW FULL COLUMNS FROM `ea_system_log_202507` [ RunTime:0.000367s ]
[2025-07-20 23:57:34][sql] INSERT INTO `ea_system_log_202507` SET `admin_id` = 6112 , `url` = '/Common/checkSession' , `method` = 'post' , `ip` = '*************' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.2.5.446' , `create_time` = 1753027054 [ RunTime:0.000158s ]
[2025-07-20 23:57:34][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000360s ]
[2025-07-20 23:57:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000133s ]
[2025-07-20 23:57:34][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000161s ]
[2025-07-20 23:57:34][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000380s ]
[2025-07-20 23:57:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000145s ]
[2025-07-20 23:57:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000201s ]
[2025-07-20 23:57:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000152s ]
[2025-07-20 23:57:34][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001260s ]
[2025-07-20 23:58:34][sql] CONNECT:[ UseTime:0.000409s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-20 23:58:34][sql] show tables like 'ea_system_log_202507' [ RunTime:0.000453s ]
[2025-07-20 23:58:34][sql] SHOW FULL COLUMNS FROM `ea_system_log_202507` [ RunTime:0.000264s ]
[2025-07-20 23:58:34][sql] INSERT INTO `ea_system_log_202507` SET `admin_id` = 6112 , `url` = '/Common/checkSession' , `method` = 'post' , `ip` = '*************' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.2.5.446' , `create_time` = 1753027114 [ RunTime:0.000124s ]
[2025-07-20 23:58:34][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000370s ]
[2025-07-20 23:58:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000146s ]
[2025-07-20 23:58:34][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000173s ]
[2025-07-20 23:58:34][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000394s ]
[2025-07-20 23:58:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000230s ]
[2025-07-20 23:58:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000221s ]
[2025-07-20 23:58:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000164s ]
[2025-07-20 23:58:34][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001394s ]
[2025-07-20 23:58:34][sql] CONNECT:[ UseTime:0.000433s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-20 23:58:34][sql] show tables like 'ea_system_log_202507' [ RunTime:0.000382s ]
[2025-07-20 23:58:34][sql] SHOW FULL COLUMNS FROM `ea_system_log_202507` [ RunTime:0.000254s ]
[2025-07-20 23:58:34][sql] INSERT INTO `ea_system_log_202507` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '*************' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.2.5.446' , `create_time` = 1753027114 [ RunTime:0.000098s ]
[2025-07-20 23:58:34][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000434s ]
[2025-07-20 23:58:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000308s ]
[2025-07-20 23:58:34][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000376s ]
[2025-07-20 23:58:34][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000409s ]
[2025-07-20 23:58:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000145s ]
[2025-07-20 23:58:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000163s ]
[2025-07-20 23:58:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000134s ]
[2025-07-20 23:58:34][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001285s ]
[2025-07-20 23:59:34][sql] CONNECT:[ UseTime:0.000396s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-20 23:59:34][sql] show tables like 'ea_system_log_202507' [ RunTime:0.000317s ]
[2025-07-20 23:59:34][sql] SHOW FULL COLUMNS FROM `ea_system_log_202507` [ RunTime:0.000199s ]
[2025-07-20 23:59:34][sql] INSERT INTO `ea_system_log_202507` SET `admin_id` = 6112 , `url` = '/home/<USER>/checkSession' , `method` = 'post' , `ip` = '*************' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.2.5.446' , `create_time` = 1753027174 [ RunTime:0.000109s ]
[2025-07-20 23:59:34][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000407s ]
[2025-07-20 23:59:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000222s ]
[2025-07-20 23:59:34][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000170s ]
[2025-07-20 23:59:34][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000361s ]
[2025-07-20 23:59:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000169s ]
[2025-07-20 23:59:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000323s ]
[2025-07-20 23:59:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000191s ]
[2025-07-20 23:59:34][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001475s ]
[2025-07-20 23:59:34][sql] CONNECT:[ UseTime:0.000316s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-20 23:59:34][sql] show tables like 'ea_system_log_202507' [ RunTime:0.000260s ]
[2025-07-20 23:59:34][sql] SHOW FULL COLUMNS FROM `ea_system_log_202507` [ RunTime:0.000208s ]
[2025-07-20 23:59:34][sql] INSERT INTO `ea_system_log_202507` SET `admin_id` = 6112 , `url` = '/Common/checkSession' , `method` = 'post' , `ip` = '*************' , `content` = '{\"sessionFlag\":\"true\"}' , `useragent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.2.5.446' , `create_time` = 1753027174 [ RunTime:0.000118s ]
[2025-07-20 23:59:34][sql] SHOW FULL COLUMNS FROM `ea_user_admin` [ RunTime:0.000286s ]
[2025-07-20 23:59:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112 LIMIT 1 [ RunTime:0.000145s ]
[2025-07-20 23:59:34][sql] SHOW FULL COLUMNS FROM `ea_user_node` [ RunTime:0.000333s ]
[2025-07-20 23:59:34][sql] SELECT `node`,`id`,`node`,`title`,`type`,`is_auth` FROM `ea_user_node` [ RunTime:0.000538s ]
[2025-07-20 23:59:34][sql] SELECT * FROM `ea_user_admin` WHERE  `id` = 6112  AND `status` = 1 LIMIT 1 [ RunTime:0.000267s ]
[2025-07-20 23:59:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth` [ RunTime:0.000212s ]
[2025-07-20 23:59:34][sql] SHOW FULL COLUMNS FROM `ea_user_auth_node` [ RunTime:0.000139s ]
[2025-07-20 23:59:34][sql] SELECT DISTINCT  `node` FROM `ea_user_node` WHERE  ( id IN ( SELECT DISTINCT  `node_id` FROM `ea_user_auth_node` WHERE  ( auth_id IN ( SELECT DISTINCT  `id` FROM `ea_user_auth` WHERE  `id` IN (45,46,48,53) ) ) ) ) [ RunTime:0.001230s ]
