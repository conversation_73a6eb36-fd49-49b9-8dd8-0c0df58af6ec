[2025-07-24 00:10:01][sql] CONNECT:[ UseTime:0.000337s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 00:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000316s ]
[2025-07-24 00:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000243s ]
[2025-07-24 00:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000413s ]
[2025-07-24 00:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000319s ]
[2025-07-24 00:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000284s ]
[2025-07-24 00:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002555s ]
[2025-07-24 00:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000306s ]
[2025-07-24 00:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000120s ]
[2025-07-24 00:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000131s ]
[2025-07-24 00:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000202s ]
[2025-07-24 00:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.304374s ]
[2025-07-24 00:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000169s ]
[2025-07-24 00:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000192s ]
[2025-07-24 00:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-24 00:10:02][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000168s ]
[2025-07-24 00:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001443s ]
[2025-07-24 00:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000158s ]
[2025-07-24 00:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-24 00:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000159s ]
[2025-07-24 00:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000141s ]
[2025-07-24 00:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000151s ]
[2025-07-24 00:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000092s ]
[2025-07-24 00:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-24 00:10:02][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000147s ]
[2025-07-24 00:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 00:10:02][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000167s ]
[2025-07-24 00:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-24 00:10:02][sql] INSERT INTO `ea_breed_data` SET `farmid` = '0216' , `batch_id` = 47 , `prod_id` = 897 , `prod_date` = 1753286400 , `cunlan` = 0 , `days` = 20293 [ RunTime:0.005320s ]
[2025-07-24 00:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000167s ]
[2025-07-24 00:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000197s ]
[2025-07-24 00:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.303365s ]
[2025-07-24 00:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000226s ]
[2025-07-24 00:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000203s ]
[2025-07-24 00:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001551s ]
[2025-07-24 00:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-24 00:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000135s ]
[2025-07-24 00:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 00:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000102s ]
[2025-07-24 00:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-24 00:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000168s ]
[2025-07-24 00:10:02][sql] INSERT INTO `ea_breed_data` SET `farmid` = '0216' , `batch_id` = 24 , `prod_id` = 872 , `prod_date` = 1753286400 , `cunlan` = 0 , `days` = 20293 [ RunTime:0.005238s ]
[2025-07-24 00:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000266s ]
[2025-07-24 00:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000251s ]
[2025-07-24 00:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.303692s ]
[2025-07-24 00:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000204s ]
[2025-07-24 00:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000166s ]
[2025-07-24 00:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001478s ]
[2025-07-24 00:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000265s ]
[2025-07-24 00:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000339s ]
[2025-07-24 00:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000162s ]
[2025-07-24 00:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000152s ]
[2025-07-24 00:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-24 00:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000101s ]
[2025-07-24 00:10:02][sql] INSERT INTO `ea_breed_data` SET `farmid` = '0216' , `batch_id` = 157 , `prod_id` = 1030 , `prod_date` = 1753286400 , `cunlan` = 37331 , `days` = 451 [ RunTime:0.005366s ]
[2025-07-24 00:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000246s ]
[2025-07-24 00:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000177s ]
[2025-07-24 00:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000446s ]
[2025-07-24 00:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000188s ]
[2025-07-24 00:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000168s ]
[2025-07-24 00:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000157s ]
[2025-07-24 00:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.304593s ]
[2025-07-24 00:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000201s ]
[2025-07-24 00:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000163s ]
[2025-07-24 00:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001457s ]
[2025-07-24 00:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000156s ]
[2025-07-24 00:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000138s ]
[2025-07-24 00:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000091s ]
[2025-07-24 00:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-24 00:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-24 00:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000098s ]
[2025-07-24 00:10:03][sql] INSERT INTO `ea_breed_data` SET `farmid` = '0216' , `batch_id` = 460 , `prod_id` = 1332 , `prod_date` = 1753286400 , `cunlan` = 17996 , `days` = 372 [ RunTime:0.005492s ]
[2025-07-24 00:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000331s ]
[2025-07-24 00:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000133s ]
[2025-07-24 00:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000171s ]
[2025-07-24 00:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000269s ]
[2025-07-24 00:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000160s ]
[2025-07-24 00:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.304693s ]
[2025-07-24 00:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000209s ]
[2025-07-24 00:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000197s ]
[2025-07-24 00:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001493s ]
[2025-07-24 00:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-24 00:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000136s ]
[2025-07-24 00:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 00:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-24 00:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-24 00:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-24 00:10:03][sql] INSERT INTO `ea_breed_data` SET `farmid` = '0216' , `batch_id` = 844 , `prod_id` = 1724 , `prod_date` = 1753286400 , `cunlan` = 0 , `days` = 20294 [ RunTime:0.005067s ]
[2025-07-24 00:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000291s ]
[2025-07-24 00:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000131s ]
[2025-07-24 00:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000111s ]
[2025-07-24 00:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000098s ]
[2025-07-24 00:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000096s ]
[2025-07-24 00:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.304219s ]
[2025-07-24 00:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000121s ]
[2025-07-24 00:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-24 00:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001550s ]
[2025-07-24 00:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000165s ]
[2025-07-24 00:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000202s ]
[2025-07-24 00:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000154s ]
[2025-07-24 00:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000148s ]
[2025-07-24 00:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000149s ]
[2025-07-24 00:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000230s ]
[2025-07-24 00:10:03][sql] INSERT INTO `ea_breed_data` SET `farmid` = '0216' , `batch_id` = 843 , `prod_id` = 1723 , `prod_date` = 1753286400 , `cunlan` = 0 , `days` = 20294 [ RunTime:0.005381s ]
[2025-07-24 00:30:01][sql] CONNECT:[ UseTime:0.000466s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 00:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-23 23' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.006744s ]
[2025-07-24 00:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-23 23' [ RunTime:0.008632s ]
[2025-07-24 00:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.003897s ]
[2025-07-24 00:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-23 23' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000455s ]
[2025-07-24 00:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-23 23' [ RunTime:0.003887s ]
[2025-07-24 01:10:01][sql] CONNECT:[ UseTime:0.000478s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000441s ]
[2025-07-24 01:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000338s ]
[2025-07-24 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000247s ]
[2025-07-24 01:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000358s ]
[2025-07-24 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000428s ]
[2025-07-24 01:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002983s ]
[2025-07-24 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000293s ]
[2025-07-24 01:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000168s ]
[2025-07-24 01:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000135s ]
[2025-07-24 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000220s ]
[2025-07-24 01:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301815s ]
[2025-07-24 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000487s ]
[2025-07-24 01:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000362s ]
[2025-07-24 01:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000318s ]
[2025-07-24 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000227s ]
[2025-07-24 01:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000179s ]
[2025-07-24 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000229s ]
[2025-07-24 01:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001527s ]
[2025-07-24 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000373s ]
[2025-07-24 01:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000297s ]
[2025-07-24 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000360s ]
[2025-07-24 01:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000206s ]
[2025-07-24 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000171s ]
[2025-07-24 01:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000097s ]
[2025-07-24 01:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-24 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000151s ]
[2025-07-24 01:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-24 01:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000093s ]
[2025-07-24 01:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753290601  WHERE (  `id` = 750030 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000125s ]
[2025-07-24 01:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000158s ]
[2025-07-24 01:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000250s ]
[2025-07-24 01:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300920s ]
[2025-07-24 01:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000359s ]
[2025-07-24 01:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000280s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000191s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001457s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000157s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000136s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-24 01:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000090s ]
[2025-07-24 01:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753290602  WHERE (  `id` = 750031 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000119s ]
[2025-07-24 01:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000186s ]
[2025-07-24 01:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000182s ]
[2025-07-24 01:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299855s ]
[2025-07-24 01:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000124s ]
[2025-07-24 01:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000091s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000156s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001583s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000135s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-24 01:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-24 01:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753290602  WHERE (  `id` = 750032 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-24 01:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000247s ]
[2025-07-24 01:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000180s ]
[2025-07-24 01:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000368s ]
[2025-07-24 01:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000180s ]
[2025-07-24 01:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000163s ]
[2025-07-24 01:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000155s ]
[2025-07-24 01:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300129s ]
[2025-07-24 01:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000223s ]
[2025-07-24 01:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000175s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000135s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001473s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000133s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-24 01:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000093s ]
[2025-07-24 01:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753290602  WHERE (  `id` = 750033 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000117s ]
[2025-07-24 01:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000330s ]
[2025-07-24 01:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000244s ]
[2025-07-24 01:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000163s ]
[2025-07-24 01:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000101s ]
[2025-07-24 01:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000097s ]
[2025-07-24 01:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299931s ]
[2025-07-24 01:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000130s ]
[2025-07-24 01:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000170s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000174s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001571s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000156s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000191s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000144s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000143s ]
[2025-07-24 01:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000140s ]
[2025-07-24 01:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000150s ]
[2025-07-24 01:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753290602  WHERE (  `id` = 750034 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000128s ]
[2025-07-24 01:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000334s ]
[2025-07-24 01:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000243s ]
[2025-07-24 01:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000174s ]
[2025-07-24 01:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000157s ]
[2025-07-24 01:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000153s ]
[2025-07-24 01:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.304481s ]
[2025-07-24 01:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000231s ]
[2025-07-24 01:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000110s ]
[2025-07-24 01:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000333s ]
[2025-07-24 01:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001592s ]
[2025-07-24 01:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000133s ]
[2025-07-24 01:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-24 01:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000092s ]
[2025-07-24 01:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-24 01:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 01:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-24 01:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753290603  WHERE (  `id` = 750035 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000163s ]
[2025-07-24 01:30:01][sql] CONNECT:[ UseTime:0.000325s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 01:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-24 00' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.006733s ]
[2025-07-24 01:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-24 00' [ RunTime:0.009846s ]
[2025-07-24 01:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004020s ]
[2025-07-24 01:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-24 00' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000416s ]
[2025-07-24 01:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-24 00' [ RunTime:0.003891s ]
[2025-07-24 02:10:02][sql] CONNECT:[ UseTime:0.000388s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 02:10:02][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000316s ]
[2025-07-24 02:10:02][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000396s ]
[2025-07-24 02:10:02][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000439s ]
[2025-07-24 02:10:02][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000320s ]
[2025-07-24 02:10:02][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000284s ]
[2025-07-24 02:10:02][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002633s ]
[2025-07-24 02:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000186s ]
[2025-07-24 02:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000113s ]
[2025-07-24 02:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000130s ]
[2025-07-24 02:10:02][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000202s ]
[2025-07-24 02:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.305378s ]
[2025-07-24 02:10:02][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000297s ]
[2025-07-24 02:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000362s ]
[2025-07-24 02:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000347s ]
[2025-07-24 02:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000283s ]
[2025-07-24 02:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000129s ]
[2025-07-24 02:10:02][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000172s ]
[2025-07-24 02:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001431s ]
[2025-07-24 02:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000155s ]
[2025-07-24 02:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-24 02:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000155s ]
[2025-07-24 02:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000140s ]
[2025-07-24 02:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000150s ]
[2025-07-24 02:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000094s ]
[2025-07-24 02:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-24 02:10:02][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000148s ]
[2025-07-24 02:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-24 02:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000092s ]
[2025-07-24 02:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753294202  WHERE (  `id` = 750030 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000120s ]
[2025-07-24 02:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000111s ]
[2025-07-24 02:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000134s ]
[2025-07-24 02:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301138s ]
[2025-07-24 02:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000283s ]
[2025-07-24 02:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000325s ]
[2025-07-24 02:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000329s ]
[2025-07-24 02:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001542s ]
[2025-07-24 02:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000168s ]
[2025-07-24 02:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000194s ]
[2025-07-24 02:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000150s ]
[2025-07-24 02:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000152s ]
[2025-07-24 02:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000147s ]
[2025-07-24 02:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000157s ]
[2025-07-24 02:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753294202  WHERE (  `id` = 750031 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000173s ]
[2025-07-24 02:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000184s ]
[2025-07-24 02:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000191s ]
[2025-07-24 02:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301973s ]
[2025-07-24 02:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000223s ]
[2025-07-24 02:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000217s ]
[2025-07-24 02:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000132s ]
[2025-07-24 02:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001491s ]
[2025-07-24 02:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-24 02:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000134s ]
[2025-07-24 02:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-24 02:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-24 02:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 02:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-24 02:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753294202  WHERE (  `id` = 750032 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-24 02:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000251s ]
[2025-07-24 02:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000252s ]
[2025-07-24 02:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000230s ]
[2025-07-24 02:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000287s ]
[2025-07-24 02:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000199s ]
[2025-07-24 02:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000111s ]
[2025-07-24 02:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301368s ]
[2025-07-24 02:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000216s ]
[2025-07-24 02:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000098s ]
[2025-07-24 02:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000158s ]
[2025-07-24 02:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001481s ]
[2025-07-24 02:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000125s ]
[2025-07-24 02:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000134s ]
[2025-07-24 02:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 02:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-24 02:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 02:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-24 02:10:03][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753294203  WHERE (  `id` = 750033 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000124s ]
[2025-07-24 02:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000329s ]
[2025-07-24 02:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000142s ]
[2025-07-24 02:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000116s ]
[2025-07-24 02:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000155s ]
[2025-07-24 02:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000104s ]
[2025-07-24 02:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301221s ]
[2025-07-24 02:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000250s ]
[2025-07-24 02:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000269s ]
[2025-07-24 02:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000172s ]
[2025-07-24 02:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001533s ]
[2025-07-24 02:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000161s ]
[2025-07-24 02:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000140s ]
[2025-07-24 02:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000091s ]
[2025-07-24 02:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-24 02:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-24 02:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-24 02:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753294203  WHERE (  `id` = 750034 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-24 02:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000284s ]
[2025-07-24 02:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000130s ]
[2025-07-24 02:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000116s ]
[2025-07-24 02:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000104s ]
[2025-07-24 02:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000297s ]
[2025-07-24 02:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301053s ]
[2025-07-24 02:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000326s ]
[2025-07-24 02:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000299s ]
[2025-07-24 02:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000180s ]
[2025-07-24 02:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001526s ]
[2025-07-24 02:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000168s ]
[2025-07-24 02:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000202s ]
[2025-07-24 02:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000150s ]
[2025-07-24 02:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000149s ]
[2025-07-24 02:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000146s ]
[2025-07-24 02:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000161s ]
[2025-07-24 02:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753294203  WHERE (  `id` = 750035 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-24 02:30:01][sql] CONNECT:[ UseTime:0.000457s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 02:30:02][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-24 01' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.007269s ]
[2025-07-24 02:30:02][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-24 01' [ RunTime:0.008843s ]
[2025-07-24 02:30:02][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.003980s ]
[2025-07-24 02:30:02][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-24 01' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000551s ]
[2025-07-24 02:30:02][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-24 01' [ RunTime:0.003890s ]
[2025-07-24 03:10:01][sql] CONNECT:[ UseTime:0.000493s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000487s ]
[2025-07-24 03:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000389s ]
[2025-07-24 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000206s ]
[2025-07-24 03:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000123s ]
[2025-07-24 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000273s ]
[2025-07-24 03:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002526s ]
[2025-07-24 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000165s ]
[2025-07-24 03:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000108s ]
[2025-07-24 03:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000131s ]
[2025-07-24 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000199s ]
[2025-07-24 03:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302013s ]
[2025-07-24 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000412s ]
[2025-07-24 03:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000241s ]
[2025-07-24 03:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000163s ]
[2025-07-24 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000229s ]
[2025-07-24 03:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000181s ]
[2025-07-24 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000235s ]
[2025-07-24 03:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001496s ]
[2025-07-24 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000226s ]
[2025-07-24 03:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000174s ]
[2025-07-24 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000235s ]
[2025-07-24 03:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000201s ]
[2025-07-24 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000219s ]
[2025-07-24 03:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000154s ]
[2025-07-24 03:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-24 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000165s ]
[2025-07-24 03:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000091s ]
[2025-07-24 03:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-24 03:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753297801  WHERE (  `id` = 750030 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000119s ]
[2025-07-24 03:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000167s ]
[2025-07-24 03:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000182s ]
[2025-07-24 03:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300718s ]
[2025-07-24 03:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000319s ]
[2025-07-24 03:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000259s ]
[2025-07-24 03:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000213s ]
[2025-07-24 03:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001533s ]
[2025-07-24 03:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000165s ]
[2025-07-24 03:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000139s ]
[2025-07-24 03:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000091s ]
[2025-07-24 03:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-24 03:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-24 03:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000092s ]
[2025-07-24 03:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753297802  WHERE (  `id` = 750031 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-24 03:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000128s ]
[2025-07-24 03:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000126s ]
[2025-07-24 03:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300478s ]
[2025-07-24 03:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000151s ]
[2025-07-24 03:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000099s ]
[2025-07-24 03:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-24 03:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001512s ]
[2025-07-24 03:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000106s ]
[2025-07-24 03:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000134s ]
[2025-07-24 03:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 03:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-24 03:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 03:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-24 03:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753297802  WHERE (  `id` = 750032 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-24 03:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000315s ]
[2025-07-24 03:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000259s ]
[2025-07-24 03:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000275s ]
[2025-07-24 03:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000174s ]
[2025-07-24 03:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000170s ]
[2025-07-24 03:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000165s ]
[2025-07-24 03:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301423s ]
[2025-07-24 03:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000129s ]
[2025-07-24 03:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000092s ]
[2025-07-24 03:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-24 03:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001575s ]
[2025-07-24 03:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000106s ]
[2025-07-24 03:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000134s ]
[2025-07-24 03:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-24 03:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-24 03:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 03:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000097s ]
[2025-07-24 03:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753297802  WHERE (  `id` = 750033 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000106s ]
[2025-07-24 03:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000327s ]
[2025-07-24 03:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000215s ]
[2025-07-24 03:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000174s ]
[2025-07-24 03:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000174s ]
[2025-07-24 03:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000121s ]
[2025-07-24 03:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.303071s ]
[2025-07-24 03:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000310s ]
[2025-07-24 03:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000185s ]
[2025-07-24 03:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000213s ]
[2025-07-24 03:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001541s ]
[2025-07-24 03:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-24 03:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000136s ]
[2025-07-24 03:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-24 03:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000150s ]
[2025-07-24 03:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000151s ]
[2025-07-24 03:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000175s ]
[2025-07-24 03:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753297803  WHERE (  `id` = 750034 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000188s ]
[2025-07-24 03:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000334s ]
[2025-07-24 03:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000248s ]
[2025-07-24 03:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000173s ]
[2025-07-24 03:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000107s ]
[2025-07-24 03:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000102s ]
[2025-07-24 03:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301115s ]
[2025-07-24 03:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000202s ]
[2025-07-24 03:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000170s ]
[2025-07-24 03:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000188s ]
[2025-07-24 03:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001470s ]
[2025-07-24 03:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000104s ]
[2025-07-24 03:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000135s ]
[2025-07-24 03:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000181s ]
[2025-07-24 03:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000200s ]
[2025-07-24 03:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000167s ]
[2025-07-24 03:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000165s ]
[2025-07-24 03:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753297803  WHERE (  `id` = 750035 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000174s ]
[2025-07-24 03:30:01][sql] CONNECT:[ UseTime:0.000485s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 03:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-24 02' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.007316s ]
[2025-07-24 03:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-24 02' [ RunTime:0.011056s ]
[2025-07-24 03:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004398s ]
[2025-07-24 03:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-24 02' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000837s ]
[2025-07-24 03:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-24 02' [ RunTime:0.004171s ]
[2025-07-24 04:10:01][sql] CONNECT:[ UseTime:0.000452s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 04:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000439s ]
[2025-07-24 04:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000264s ]
[2025-07-24 04:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000256s ]
[2025-07-24 04:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000121s ]
[2025-07-24 04:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000252s ]
[2025-07-24 04:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002518s ]
[2025-07-24 04:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000164s ]
[2025-07-24 04:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000107s ]
[2025-07-24 04:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000128s ]
[2025-07-24 04:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000197s ]
[2025-07-24 04:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.304251s ]
[2025-07-24 04:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000263s ]
[2025-07-24 04:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000264s ]
[2025-07-24 04:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000294s ]
[2025-07-24 04:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000345s ]
[2025-07-24 04:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000184s ]
[2025-07-24 04:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000227s ]
[2025-07-24 04:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001530s ]
[2025-07-24 04:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000173s ]
[2025-07-24 04:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-24 04:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000163s ]
[2025-07-24 04:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000195s ]
[2025-07-24 04:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000214s ]
[2025-07-24 04:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000144s ]
[2025-07-24 04:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-24 04:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000167s ]
[2025-07-24 04:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000091s ]
[2025-07-24 04:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-24 04:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753301401  WHERE (  `id` = 750030 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-24 04:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000158s ]
[2025-07-24 04:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000194s ]
[2025-07-24 04:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302404s ]
[2025-07-24 04:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000317s ]
[2025-07-24 04:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000305s ]
[2025-07-24 04:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000317s ]
[2025-07-24 04:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001550s ]
[2025-07-24 04:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-24 04:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-24 04:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000141s ]
[2025-07-24 04:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-24 04:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-24 04:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000091s ]
[2025-07-24 04:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753301401  WHERE (  `id` = 750031 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-24 04:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000180s ]
[2025-07-24 04:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000185s ]
[2025-07-24 04:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302410s ]
[2025-07-24 04:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000222s ]
[2025-07-24 04:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000177s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000140s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001467s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000106s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000133s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-24 04:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-24 04:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753301402  WHERE (  `id` = 750032 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-24 04:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000239s ]
[2025-07-24 04:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000130s ]
[2025-07-24 04:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000225s ]
[2025-07-24 04:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000128s ]
[2025-07-24 04:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000110s ]
[2025-07-24 04:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000106s ]
[2025-07-24 04:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302743s ]
[2025-07-24 04:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000134s ]
[2025-07-24 04:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000092s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001482s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000106s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000134s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 04:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-24 04:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753301402  WHERE (  `id` = 750033 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-24 04:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000396s ]
[2025-07-24 04:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000252s ]
[2025-07-24 04:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000173s ]
[2025-07-24 04:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000174s ]
[2025-07-24 04:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000163s ]
[2025-07-24 04:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302422s ]
[2025-07-24 04:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000296s ]
[2025-07-24 04:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000227s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000164s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001491s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000104s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000134s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 04:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-24 04:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753301402  WHERE (  `id` = 750034 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000115s ]
[2025-07-24 04:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000329s ]
[2025-07-24 04:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000186s ]
[2025-07-24 04:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000175s ]
[2025-07-24 04:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000167s ]
[2025-07-24 04:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000161s ]
[2025-07-24 04:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302579s ]
[2025-07-24 04:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000304s ]
[2025-07-24 04:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000297s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000217s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001479s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000187s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000222s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000098s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-24 04:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 04:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-24 04:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753301402  WHERE (  `id` = 750035 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-24 04:30:01][sql] CONNECT:[ UseTime:0.000435s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 04:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-24 03' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.006941s ]
[2025-07-24 04:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-24 03' [ RunTime:0.009956s ]
[2025-07-24 04:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004162s ]
[2025-07-24 04:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-24 03' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000954s ]
[2025-07-24 04:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-24 03' [ RunTime:0.004573s ]
[2025-07-24 05:10:01][sql] CONNECT:[ UseTime:0.000447s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000303s ]
[2025-07-24 05:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000238s ]
[2025-07-24 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000247s ]
[2025-07-24 05:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000127s ]
[2025-07-24 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000271s ]
[2025-07-24 05:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002492s ]
[2025-07-24 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000232s ]
[2025-07-24 05:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000252s ]
[2025-07-24 05:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000187s ]
[2025-07-24 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000260s ]
[2025-07-24 05:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.305146s ]
[2025-07-24 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000421s ]
[2025-07-24 05:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000252s ]
[2025-07-24 05:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000169s ]
[2025-07-24 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000233s ]
[2025-07-24 05:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000120s ]
[2025-07-24 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000173s ]
[2025-07-24 05:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001499s ]
[2025-07-24 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000161s ]
[2025-07-24 05:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-24 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000157s ]
[2025-07-24 05:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000139s ]
[2025-07-24 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000153s ]
[2025-07-24 05:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000092s ]
[2025-07-24 05:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-24 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000148s ]
[2025-07-24 05:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 05:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000151s ]
[2025-07-24 05:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753305001  WHERE (  `id` = 750030 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-24 05:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000131s ]
[2025-07-24 05:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000163s ]
[2025-07-24 05:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.309428s ]
[2025-07-24 05:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000290s ]
[2025-07-24 05:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000199s ]
[2025-07-24 05:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000143s ]
[2025-07-24 05:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001479s ]
[2025-07-24 05:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-24 05:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000138s ]
[2025-07-24 05:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-24 05:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-24 05:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 05:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000089s ]
[2025-07-24 05:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753305002  WHERE (  `id` = 750031 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000124s ]
[2025-07-24 05:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000186s ]
[2025-07-24 05:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000255s ]
[2025-07-24 05:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.303443s ]
[2025-07-24 05:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000321s ]
[2025-07-24 05:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000292s ]
[2025-07-24 05:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000196s ]
[2025-07-24 05:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001485s ]
[2025-07-24 05:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000247s ]
[2025-07-24 05:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000228s ]
[2025-07-24 05:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000099s ]
[2025-07-24 05:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-24 05:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-24 05:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000099s ]
[2025-07-24 05:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753305002  WHERE (  `id` = 750032 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-24 05:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000255s ]
[2025-07-24 05:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000344s ]
[2025-07-24 05:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000386s ]
[2025-07-24 05:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000190s ]
[2025-07-24 05:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000171s ]
[2025-07-24 05:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000170s ]
[2025-07-24 05:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.304705s ]
[2025-07-24 05:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000235s ]
[2025-07-24 05:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000142s ]
[2025-07-24 05:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000115s ]
[2025-07-24 05:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001566s ]
[2025-07-24 05:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-24 05:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000136s ]
[2025-07-24 05:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000135s ]
[2025-07-24 05:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-24 05:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-24 05:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-24 05:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753305002  WHERE (  `id` = 750033 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000115s ]
[2025-07-24 05:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000306s ]
[2025-07-24 05:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000129s ]
[2025-07-24 05:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000125s ]
[2025-07-24 05:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000111s ]
[2025-07-24 05:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000105s ]
[2025-07-24 05:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.303475s ]
[2025-07-24 05:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000312s ]
[2025-07-24 05:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000299s ]
[2025-07-24 05:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000321s ]
[2025-07-24 05:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001550s ]
[2025-07-24 05:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-24 05:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-24 05:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-24 05:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-24 05:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 05:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000176s ]
[2025-07-24 05:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753305003  WHERE (  `id` = 750034 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000170s ]
[2025-07-24 05:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000336s ]
[2025-07-24 05:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000185s ]
[2025-07-24 05:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000170s ]
[2025-07-24 05:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000167s ]
[2025-07-24 05:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000166s ]
[2025-07-24 05:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.303664s ]
[2025-07-24 05:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000285s ]
[2025-07-24 05:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000153s ]
[2025-07-24 05:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000119s ]
[2025-07-24 05:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001437s ]
[2025-07-24 05:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000149s ]
[2025-07-24 05:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-24 05:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-24 05:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-24 05:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 05:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000098s ]
[2025-07-24 05:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753305003  WHERE (  `id` = 750035 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-24 05:30:01][sql] CONNECT:[ UseTime:0.000554s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 05:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-24 04' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.006954s ]
[2025-07-24 05:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-24 04' [ RunTime:0.009501s ]
[2025-07-24 05:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.003853s ]
[2025-07-24 05:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-24 04' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000380s ]
[2025-07-24 05:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-24 04' [ RunTime:0.003914s ]
[2025-07-24 06:10:01][sql] CONNECT:[ UseTime:0.000421s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000450s ]
[2025-07-24 06:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000201s ]
[2025-07-24 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000255s ]
[2025-07-24 06:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000126s ]
[2025-07-24 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000406s ]
[2025-07-24 06:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002701s ]
[2025-07-24 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000226s ]
[2025-07-24 06:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000177s ]
[2025-07-24 06:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000209s ]
[2025-07-24 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000213s ]
[2025-07-24 06:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300990s ]
[2025-07-24 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000379s ]
[2025-07-24 06:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000299s ]
[2025-07-24 06:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000165s ]
[2025-07-24 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000225s ]
[2025-07-24 06:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000120s ]
[2025-07-24 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000169s ]
[2025-07-24 06:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001448s ]
[2025-07-24 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000203s ]
[2025-07-24 06:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-24 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000154s ]
[2025-07-24 06:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000141s ]
[2025-07-24 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000150s ]
[2025-07-24 06:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000094s ]
[2025-07-24 06:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-24 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000148s ]
[2025-07-24 06:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-24 06:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-24 06:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753308601  WHERE (  `id` = 750030 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000119s ]
[2025-07-24 06:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000161s ]
[2025-07-24 06:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000183s ]
[2025-07-24 06:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299831s ]
[2025-07-24 06:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000302s ]
[2025-07-24 06:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000261s ]
[2025-07-24 06:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000282s ]
[2025-07-24 06:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001508s ]
[2025-07-24 06:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000298s ]
[2025-07-24 06:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000186s ]
[2025-07-24 06:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000100s ]
[2025-07-24 06:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-24 06:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-24 06:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000092s ]
[2025-07-24 06:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753308601  WHERE (  `id` = 750031 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-24 06:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000177s ]
[2025-07-24 06:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000186s ]
[2025-07-24 06:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299829s ]
[2025-07-24 06:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000180s ]
[2025-07-24 06:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000130s ]
[2025-07-24 06:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000160s ]
[2025-07-24 06:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001634s ]
[2025-07-24 06:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000169s ]
[2025-07-24 06:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000139s ]
[2025-07-24 06:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-24 06:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-24 06:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 06:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000097s ]
[2025-07-24 06:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753308602  WHERE (  `id` = 750032 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-24 06:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000194s ]
[2025-07-24 06:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000125s ]
[2025-07-24 06:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000251s ]
[2025-07-24 06:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000135s ]
[2025-07-24 06:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000110s ]
[2025-07-24 06:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000107s ]
[2025-07-24 06:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299918s ]
[2025-07-24 06:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000339s ]
[2025-07-24 06:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000337s ]
[2025-07-24 06:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000201s ]
[2025-07-24 06:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001540s ]
[2025-07-24 06:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-24 06:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000297s ]
[2025-07-24 06:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000289s ]
[2025-07-24 06:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000158s ]
[2025-07-24 06:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-24 06:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000099s ]
[2025-07-24 06:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753308602  WHERE (  `id` = 750033 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-24 06:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000340s ]
[2025-07-24 06:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000184s ]
[2025-07-24 06:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000128s ]
[2025-07-24 06:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000108s ]
[2025-07-24 06:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000104s ]
[2025-07-24 06:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300103s ]
[2025-07-24 06:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000284s ]
[2025-07-24 06:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000252s ]
[2025-07-24 06:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000346s ]
[2025-07-24 06:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001547s ]
[2025-07-24 06:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000169s ]
[2025-07-24 06:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000143s ]
[2025-07-24 06:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000092s ]
[2025-07-24 06:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-24 06:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-24 06:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-24 06:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753308602  WHERE (  `id` = 750034 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000116s ]
[2025-07-24 06:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000335s ]
[2025-07-24 06:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000248s ]
[2025-07-24 06:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000190s ]
[2025-07-24 06:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000168s ]
[2025-07-24 06:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000165s ]
[2025-07-24 06:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299775s ]
[2025-07-24 06:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000340s ]
[2025-07-24 06:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000304s ]
[2025-07-24 06:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000126s ]
[2025-07-24 06:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001484s ]
[2025-07-24 06:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000106s ]
[2025-07-24 06:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000136s ]
[2025-07-24 06:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-24 06:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-24 06:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 06:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-24 06:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753308603  WHERE (  `id` = 750035 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-24 06:30:01][sql] CONNECT:[ UseTime:0.000388s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 06:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-24 05' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.006796s ]
[2025-07-24 06:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-24 05' [ RunTime:0.013595s ]
[2025-07-24 06:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004555s ]
[2025-07-24 06:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-24 05' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.001249s ]
[2025-07-24 06:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-24 05' [ RunTime:0.002586s ]
[2025-07-24 07:10:01][sql] CONNECT:[ UseTime:0.000409s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 07:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000518s ]
[2025-07-24 07:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000198s ]
[2025-07-24 07:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000246s ]
[2025-07-24 07:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000154s ]
[2025-07-24 07:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000447s ]
[2025-07-24 07:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.003613s ]
[2025-07-24 07:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000376s ]
[2025-07-24 07:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000232s ]
[2025-07-24 07:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000268s ]
[2025-07-24 07:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000406s ]
[2025-07-24 07:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.305568s ]
[2025-07-24 07:10:02][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000417s ]
[2025-07-24 07:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000324s ]
[2025-07-24 07:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000278s ]
[2025-07-24 07:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000222s ]
[2025-07-24 07:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000171s ]
[2025-07-24 07:10:02][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000235s ]
[2025-07-24 07:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001517s ]
[2025-07-24 07:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000228s ]
[2025-07-24 07:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000172s ]
[2025-07-24 07:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000227s ]
[2025-07-24 07:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000209s ]
[2025-07-24 07:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000221s ]
[2025-07-24 07:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000157s ]
[2025-07-24 07:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000150s ]
[2025-07-24 07:10:02][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000168s ]
[2025-07-24 07:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000094s ]
[2025-07-24 07:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-24 07:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753312202  WHERE (  `id` = 750030 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000117s ]
[2025-07-24 07:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000165s ]
[2025-07-24 07:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000189s ]
[2025-07-24 07:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300727s ]
[2025-07-24 07:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000301s ]
[2025-07-24 07:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000176s ]
[2025-07-24 07:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000183s ]
[2025-07-24 07:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001555s ]
[2025-07-24 07:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-24 07:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000135s ]
[2025-07-24 07:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 07:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-24 07:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 07:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000092s ]
[2025-07-24 07:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753312202  WHERE (  `id` = 750031 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-24 07:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000181s ]
[2025-07-24 07:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000181s ]
[2025-07-24 07:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300660s ]
[2025-07-24 07:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000351s ]
[2025-07-24 07:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000328s ]
[2025-07-24 07:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000188s ]
[2025-07-24 07:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001453s ]
[2025-07-24 07:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000105s ]
[2025-07-24 07:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-24 07:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-24 07:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-24 07:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-24 07:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000258s ]
[2025-07-24 07:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753312202  WHERE (  `id` = 750032 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000314s ]
[2025-07-24 07:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000219s ]
[2025-07-24 07:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000134s ]
[2025-07-24 07:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000268s ]
[2025-07-24 07:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000138s ]
[2025-07-24 07:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000116s ]
[2025-07-24 07:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000111s ]
[2025-07-24 07:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300527s ]
[2025-07-24 07:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000264s ]
[2025-07-24 07:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000189s ]
[2025-07-24 07:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000199s ]
[2025-07-24 07:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001490s ]
[2025-07-24 07:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000160s ]
[2025-07-24 07:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000139s ]
[2025-07-24 07:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-24 07:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-24 07:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000116s ]
[2025-07-24 07:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000106s ]
[2025-07-24 07:10:03][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753312203  WHERE (  `id` = 750033 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-24 07:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000335s ]
[2025-07-24 07:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000132s ]
[2025-07-24 07:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000133s ]
[2025-07-24 07:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000118s ]
[2025-07-24 07:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000113s ]
[2025-07-24 07:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301430s ]
[2025-07-24 07:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000293s ]
[2025-07-24 07:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000296s ]
[2025-07-24 07:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000338s ]
[2025-07-24 07:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001587s ]
[2025-07-24 07:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000179s ]
[2025-07-24 07:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000339s ]
[2025-07-24 07:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000284s ]
[2025-07-24 07:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000159s ]
[2025-07-24 07:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000152s ]
[2025-07-24 07:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000162s ]
[2025-07-24 07:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753312203  WHERE (  `id` = 750034 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000177s ]
[2025-07-24 07:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000347s ]
[2025-07-24 07:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000241s ]
[2025-07-24 07:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000310s ]
[2025-07-24 07:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000188s ]
[2025-07-24 07:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000184s ]
[2025-07-24 07:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300628s ]
[2025-07-24 07:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000258s ]
[2025-07-24 07:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000155s ]
[2025-07-24 07:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-24 07:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001490s ]
[2025-07-24 07:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000155s ]
[2025-07-24 07:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-24 07:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-24 07:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-24 07:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 07:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-24 07:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753312203  WHERE (  `id` = 750035 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-24 07:30:01][sql] CONNECT:[ UseTime:0.000498s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 07:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-24 06' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.006888s ]
[2025-07-24 07:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-24 06' [ RunTime:0.009115s ]
[2025-07-24 07:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004585s ]
[2025-07-24 07:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-24 06' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000690s ]
[2025-07-24 07:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-24 06' [ RunTime:0.004054s ]
[2025-07-24 08:10:01][sql] CONNECT:[ UseTime:0.000465s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000517s ]
[2025-07-24 08:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000245s ]
[2025-07-24 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000358s ]
[2025-07-24 08:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000175s ]
[2025-07-24 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000278s ]
[2025-07-24 08:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002629s ]
[2025-07-24 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000223s ]
[2025-07-24 08:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000116s ]
[2025-07-24 08:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000132s ]
[2025-07-24 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000411s ]
[2025-07-24 08:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.303834s ]
[2025-07-24 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000421s ]
[2025-07-24 08:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000330s ]
[2025-07-24 08:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000316s ]
[2025-07-24 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000218s ]
[2025-07-24 08:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000175s ]
[2025-07-24 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000227s ]
[2025-07-24 08:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001509s ]
[2025-07-24 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000214s ]
[2025-07-24 08:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000162s ]
[2025-07-24 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000216s ]
[2025-07-24 08:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000196s ]
[2025-07-24 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000212s ]
[2025-07-24 08:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000145s ]
[2025-07-24 08:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-24 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000165s ]
[2025-07-24 08:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000091s ]
[2025-07-24 08:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000091s ]
[2025-07-24 08:10:01][sql] UPDATE `ea_breed_data`  SET `days` = 20294 , `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753315801  WHERE (  `id` = 750030 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000120s ]
[2025-07-24 08:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000215s ]
[2025-07-24 08:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000279s ]
[2025-07-24 08:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301331s ]
[2025-07-24 08:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000297s ]
[2025-07-24 08:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000155s ]
[2025-07-24 08:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000115s ]
[2025-07-24 08:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001525s ]
[2025-07-24 08:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-24 08:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000136s ]
[2025-07-24 08:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-24 08:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-24 08:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000079s ]
[2025-07-24 08:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000088s ]
[2025-07-24 08:10:01][sql] UPDATE `ea_breed_data`  SET `days` = 20294 , `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753315801  WHERE (  `id` = 750031 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000121s ]
[2025-07-24 08:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000205s ]
[2025-07-24 08:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000186s ]
[2025-07-24 08:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302381s ]
[2025-07-24 08:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000319s ]
[2025-07-24 08:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000238s ]
[2025-07-24 08:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000172s ]
[2025-07-24 08:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001533s ]
[2025-07-24 08:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000158s ]
[2025-07-24 08:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000138s ]
[2025-07-24 08:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-24 08:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-24 08:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-24 08:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-24 08:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753315802  WHERE (  `id` = 750032 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-24 08:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000249s ]
[2025-07-24 08:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000133s ]
[2025-07-24 08:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000288s ]
[2025-07-24 08:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000267s ]
[2025-07-24 08:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000175s ]
[2025-07-24 08:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000175s ]
[2025-07-24 08:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302412s ]
[2025-07-24 08:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000267s ]
[2025-07-24 08:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000189s ]
[2025-07-24 08:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000170s ]
[2025-07-24 08:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001540s ]
[2025-07-24 08:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-24 08:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000136s ]
[2025-07-24 08:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 08:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-24 08:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000094s ]
[2025-07-24 08:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-24 08:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753315802  WHERE (  `id` = 750033 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000120s ]
[2025-07-24 08:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000343s ]
[2025-07-24 08:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000317s ]
[2025-07-24 08:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000197s ]
[2025-07-24 08:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000178s ]
[2025-07-24 08:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000176s ]
[2025-07-24 08:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302297s ]
[2025-07-24 08:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000239s ]
[2025-07-24 08:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000343s ]
[2025-07-24 08:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000357s ]
[2025-07-24 08:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001687s ]
[2025-07-24 08:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000170s ]
[2025-07-24 08:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000140s ]
[2025-07-24 08:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000091s ]
[2025-07-24 08:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-24 08:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 08:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-24 08:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753315802  WHERE (  `id` = 750034 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000139s ]
[2025-07-24 08:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000443s ]
[2025-07-24 08:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000161s ]
[2025-07-24 08:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000161s ]
[2025-07-24 08:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000137s ]
[2025-07-24 08:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000119s ]
[2025-07-24 08:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302702s ]
[2025-07-24 08:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000335s ]
[2025-07-24 08:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000161s ]
[2025-07-24 08:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000215s ]
[2025-07-24 08:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001513s ]
[2025-07-24 08:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000173s ]
[2025-07-24 08:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000322s ]
[2025-07-24 08:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000268s ]
[2025-07-24 08:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000184s ]
[2025-07-24 08:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000125s ]
[2025-07-24 08:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000100s ]
[2025-07-24 08:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753315803  WHERE (  `id` = 750035 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-24 08:30:01][sql] CONNECT:[ UseTime:0.000361s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 08:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-24 07' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.006566s ]
[2025-07-24 08:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-24 07' [ RunTime:0.008662s ]
[2025-07-24 08:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004144s ]
[2025-07-24 08:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-24 07' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000293s ]
[2025-07-24 08:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-24 07' [ RunTime:0.003983s ]
[2025-07-24 09:10:01][sql] CONNECT:[ UseTime:0.000460s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 09:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000454s ]
[2025-07-24 09:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000196s ]
[2025-07-24 09:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000195s ]
[2025-07-24 09:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000123s ]
[2025-07-24 09:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000397s ]
[2025-07-24 09:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002585s ]
[2025-07-24 09:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000179s ]
[2025-07-24 09:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000111s ]
[2025-07-24 09:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000130s ]
[2025-07-24 09:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000299s ]
[2025-07-24 09:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.304951s ]
[2025-07-24 09:10:02][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000449s ]
[2025-07-24 09:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000343s ]
[2025-07-24 09:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000196s ]
[2025-07-24 09:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000223s ]
[2025-07-24 09:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000123s ]
[2025-07-24 09:10:02][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000183s ]
[2025-07-24 09:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001466s ]
[2025-07-24 09:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000203s ]
[2025-07-24 09:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-24 09:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000158s ]
[2025-07-24 09:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000140s ]
[2025-07-24 09:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000151s ]
[2025-07-24 09:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000094s ]
[2025-07-24 09:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-24 09:10:02][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000150s ]
[2025-07-24 09:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 09:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-24 09:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753319402  WHERE (  `id` = 750030 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000118s ]
[2025-07-24 09:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000167s ]
[2025-07-24 09:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000195s ]
[2025-07-24 09:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302815s ]
[2025-07-24 09:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000311s ]
[2025-07-24 09:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000165s ]
[2025-07-24 09:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000173s ]
[2025-07-24 09:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001434s ]
[2025-07-24 09:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000104s ]
[2025-07-24 09:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000133s ]
[2025-07-24 09:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 09:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-24 09:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 09:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000091s ]
[2025-07-24 09:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753319402  WHERE (  `id` = 750031 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-24 09:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000235s ]
[2025-07-24 09:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000252s ]
[2025-07-24 09:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302859s ]
[2025-07-24 09:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000230s ]
[2025-07-24 09:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000231s ]
[2025-07-24 09:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000200s ]
[2025-07-24 09:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001460s ]
[2025-07-24 09:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000106s ]
[2025-07-24 09:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000143s ]
[2025-07-24 09:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-24 09:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-24 09:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 09:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-24 09:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753319402  WHERE (  `id` = 750032 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-24 09:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000262s ]
[2025-07-24 09:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000194s ]
[2025-07-24 09:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000238s ]
[2025-07-24 09:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000136s ]
[2025-07-24 09:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000116s ]
[2025-07-24 09:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000109s ]
[2025-07-24 09:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.303452s ]
[2025-07-24 09:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000146s ]
[2025-07-24 09:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-24 09:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-24 09:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001438s ]
[2025-07-24 09:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-24 09:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000134s ]
[2025-07-24 09:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-24 09:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-24 09:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-24 09:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-24 09:10:03][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753319403  WHERE (  `id` = 750033 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-24 09:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000354s ]
[2025-07-24 09:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000272s ]
[2025-07-24 09:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000137s ]
[2025-07-24 09:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000119s ]
[2025-07-24 09:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000115s ]
[2025-07-24 09:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.303679s ]
[2025-07-24 09:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000144s ]
[2025-07-24 09:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000233s ]
[2025-07-24 09:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000199s ]
[2025-07-24 09:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001450s ]
[2025-07-24 09:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000106s ]
[2025-07-24 09:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000135s ]
[2025-07-24 09:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-24 09:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-24 09:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-24 09:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-24 09:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753319403  WHERE (  `id` = 750034 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-24 09:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000282s ]
[2025-07-24 09:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000129s ]
[2025-07-24 09:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000307s ]
[2025-07-24 09:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000230s ]
[2025-07-24 09:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000188s ]
[2025-07-24 09:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.303301s ]
[2025-07-24 09:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000254s ]
[2025-07-24 09:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000103s ]
[2025-07-24 09:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000117s ]
[2025-07-24 09:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001469s ]
[2025-07-24 09:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-24 09:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000135s ]
[2025-07-24 09:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-24 09:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-24 09:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 09:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-24 09:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753319403  WHERE (  `id` = 750035 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000178s ]
[2025-07-24 09:30:01][sql] CONNECT:[ UseTime:0.000387s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 09:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-24 08' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.006501s ]
[2025-07-24 09:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-24 08' [ RunTime:0.008470s ]
[2025-07-24 09:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004397s ]
[2025-07-24 09:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-24 08' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000371s ]
[2025-07-24 09:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-24 08' [ RunTime:0.002081s ]
[2025-07-24 10:10:01][sql] CONNECT:[ UseTime:0.000288s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000574s ]
[2025-07-24 10:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000410s ]
[2025-07-24 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000352s ]
[2025-07-24 10:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000210s ]
[2025-07-24 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000262s ]
[2025-07-24 10:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002523s ]
[2025-07-24 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000213s ]
[2025-07-24 10:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000110s ]
[2025-07-24 10:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000131s ]
[2025-07-24 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000196s ]
[2025-07-24 10:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300929s ]
[2025-07-24 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000218s ]
[2025-07-24 10:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000153s ]
[2025-07-24 10:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000182s ]
[2025-07-24 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000210s ]
[2025-07-24 10:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-24 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000165s ]
[2025-07-24 10:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001427s ]
[2025-07-24 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000151s ]
[2025-07-24 10:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-24 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000156s ]
[2025-07-24 10:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000139s ]
[2025-07-24 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000151s ]
[2025-07-24 10:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000105s ]
[2025-07-24 10:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000154s ]
[2025-07-24 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000219s ]
[2025-07-24 10:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000152s ]
[2025-07-24 10:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000158s ]
[2025-07-24 10:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753323001  WHERE (  `id` = 750030 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000184s ]
[2025-07-24 10:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000159s ]
[2025-07-24 10:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000186s ]
[2025-07-24 10:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300291s ]
[2025-07-24 10:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000135s ]
[2025-07-24 10:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-24 10:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000198s ]
[2025-07-24 10:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001503s ]
[2025-07-24 10:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-24 10:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000154s ]
[2025-07-24 10:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000149s ]
[2025-07-24 10:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-24 10:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-24 10:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000093s ]
[2025-07-24 10:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753323002  WHERE (  `id` = 750031 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-24 10:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000188s ]
[2025-07-24 10:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000254s ]
[2025-07-24 10:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.304163s ]
[2025-07-24 10:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000301s ]
[2025-07-24 10:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000198s ]
[2025-07-24 10:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000359s ]
[2025-07-24 10:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001754s ]
[2025-07-24 10:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000311s ]
[2025-07-24 10:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000212s ]
[2025-07-24 10:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000131s ]
[2025-07-24 10:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000094s ]
[2025-07-24 10:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-24 10:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000099s ]
[2025-07-24 10:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753323002  WHERE (  `id` = 750032 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000129s ]
[2025-07-24 10:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000212s ]
[2025-07-24 10:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000137s ]
[2025-07-24 10:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000243s ]
[2025-07-24 10:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000214s ]
[2025-07-24 10:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000158s ]
[2025-07-24 10:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000206s ]
[2025-07-24 10:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301192s ]
[2025-07-24 10:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000203s ]
[2025-07-24 10:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000190s ]
[2025-07-24 10:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000117s ]
[2025-07-24 10:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001443s ]
[2025-07-24 10:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-24 10:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-24 10:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000131s ]
[2025-07-24 10:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-24 10:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 10:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-24 10:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753323002  WHERE (  `id` = 750033 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000105s ]
[2025-07-24 10:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000324s ]
[2025-07-24 10:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000128s ]
[2025-07-24 10:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000127s ]
[2025-07-24 10:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000197s ]
[2025-07-24 10:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000170s ]
[2025-07-24 10:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301190s ]
[2025-07-24 10:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000298s ]
[2025-07-24 10:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000235s ]
[2025-07-24 10:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000185s ]
[2025-07-24 10:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001509s ]
[2025-07-24 10:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000163s ]
[2025-07-24 10:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000189s ]
[2025-07-24 10:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000141s ]
[2025-07-24 10:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000144s ]
[2025-07-24 10:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000145s ]
[2025-07-24 10:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000097s ]
[2025-07-24 10:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753323003  WHERE (  `id` = 750034 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-24 10:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000412s ]
[2025-07-24 10:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000298s ]
[2025-07-24 10:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000197s ]
[2025-07-24 10:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000185s ]
[2025-07-24 10:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000187s ]
[2025-07-24 10:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301454s ]
[2025-07-24 10:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000244s ]
[2025-07-24 10:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000098s ]
[2025-07-24 10:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-24 10:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001459s ]
[2025-07-24 10:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000159s ]
[2025-07-24 10:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-24 10:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 10:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-24 10:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000079s ]
[2025-07-24 10:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-24 10:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753323003  WHERE (  `id` = 750035 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-24 10:30:01][sql] CONNECT:[ UseTime:0.000387s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 10:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-24 09' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.006282s ]
[2025-07-24 10:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-24 09' [ RunTime:0.008452s ]
[2025-07-24 10:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.003904s ]
[2025-07-24 10:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-24 09' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000367s ]
[2025-07-24 10:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-24 09' [ RunTime:0.003929s ]
[2025-07-24 11:10:01][sql] CONNECT:[ UseTime:0.000418s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 11:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000314s ]
[2025-07-24 11:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000233s ]
[2025-07-24 11:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000367s ]
[2025-07-24 11:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000246s ]
[2025-07-24 11:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000275s ]
[2025-07-24 11:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002606s ]
[2025-07-24 11:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000232s ]
[2025-07-24 11:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000172s ]
[2025-07-24 11:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000193s ]
[2025-07-24 11:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000261s ]
[2025-07-24 11:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300526s ]
[2025-07-24 11:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000420s ]
[2025-07-24 11:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000226s ]
[2025-07-24 11:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000169s ]
[2025-07-24 11:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000229s ]
[2025-07-24 11:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000187s ]
[2025-07-24 11:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000233s ]
[2025-07-24 11:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001494s ]
[2025-07-24 11:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000223s ]
[2025-07-24 11:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000169s ]
[2025-07-24 11:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000227s ]
[2025-07-24 11:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000202s ]
[2025-07-24 11:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000219s ]
[2025-07-24 11:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000100s ]
[2025-07-24 11:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-24 11:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000153s ]
[2025-07-24 11:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000151s ]
[2025-07-24 11:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000159s ]
[2025-07-24 11:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753326601  WHERE (  `id` = 750030 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000182s ]
[2025-07-24 11:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000160s ]
[2025-07-24 11:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000187s ]
[2025-07-24 11:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300651s ]
[2025-07-24 11:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000234s ]
[2025-07-24 11:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000097s ]
[2025-07-24 11:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000133s ]
[2025-07-24 11:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001618s ]
[2025-07-24 11:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000173s ]
[2025-07-24 11:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000195s ]
[2025-07-24 11:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000148s ]
[2025-07-24 11:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000142s ]
[2025-07-24 11:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000138s ]
[2025-07-24 11:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000146s ]
[2025-07-24 11:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753326601  WHERE (  `id` = 750031 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000122s ]
[2025-07-24 11:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000142s ]
[2025-07-24 11:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000134s ]
[2025-07-24 11:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302321s ]
[2025-07-24 11:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000268s ]
[2025-07-24 11:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000286s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000295s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001513s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000178s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000196s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000144s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000156s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-24 11:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000100s ]
[2025-07-24 11:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753326602  WHERE (  `id` = 750032 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000129s ]
[2025-07-24 11:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000261s ]
[2025-07-24 11:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000197s ]
[2025-07-24 11:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000280s ]
[2025-07-24 11:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000202s ]
[2025-07-24 11:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000127s ]
[2025-07-24 11:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000114s ]
[2025-07-24 11:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301152s ]
[2025-07-24 11:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000305s ]
[2025-07-24 11:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000309s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000264s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001530s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000287s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000348s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000286s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000242s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000144s ]
[2025-07-24 11:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000218s ]
[2025-07-24 11:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753326602  WHERE (  `id` = 750033 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000146s ]
[2025-07-24 11:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000472s ]
[2025-07-24 11:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000250s ]
[2025-07-24 11:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000192s ]
[2025-07-24 11:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000120s ]
[2025-07-24 11:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000121s ]
[2025-07-24 11:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301102s ]
[2025-07-24 11:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000266s ]
[2025-07-24 11:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000283s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000289s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001514s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000349s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000213s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000151s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000145s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000144s ]
[2025-07-24 11:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000157s ]
[2025-07-24 11:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753326602  WHERE (  `id` = 750034 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000169s ]
[2025-07-24 11:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000348s ]
[2025-07-24 11:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000282s ]
[2025-07-24 11:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000285s ]
[2025-07-24 11:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000134s ]
[2025-07-24 11:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000124s ]
[2025-07-24 11:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301749s ]
[2025-07-24 11:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000354s ]
[2025-07-24 11:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000303s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000200s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001546s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000186s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000198s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000151s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000149s ]
[2025-07-24 11:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000148s ]
[2025-07-24 11:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000160s ]
[2025-07-24 11:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753326602  WHERE (  `id` = 750035 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000177s ]
[2025-07-24 11:30:01][sql] CONNECT:[ UseTime:0.000458s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 11:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-24 10' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.007517s ]
[2025-07-24 11:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-24 10' [ RunTime:0.007836s ]
[2025-07-24 11:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.003753s ]
[2025-07-24 11:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-24 10' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000607s ]
[2025-07-24 11:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-24 10' [ RunTime:0.003904s ]
[2025-07-24 12:10:01][sql] CONNECT:[ UseTime:0.000441s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000510s ]
[2025-07-24 12:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000279s ]
[2025-07-24 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000211s ]
[2025-07-24 12:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000120s ]
[2025-07-24 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000326s ]
[2025-07-24 12:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002498s ]
[2025-07-24 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000206s ]
[2025-07-24 12:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000135s ]
[2025-07-24 12:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000159s ]
[2025-07-24 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000245s ]
[2025-07-24 12:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299847s ]
[2025-07-24 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000357s ]
[2025-07-24 12:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000169s ]
[2025-07-24 12:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000151s ]
[2025-07-24 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000225s ]
[2025-07-24 12:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000169s ]
[2025-07-24 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000246s ]
[2025-07-24 12:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001516s ]
[2025-07-24 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000226s ]
[2025-07-24 12:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000166s ]
[2025-07-24 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000222s ]
[2025-07-24 12:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000204s ]
[2025-07-24 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000215s ]
[2025-07-24 12:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000146s ]
[2025-07-24 12:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000142s ]
[2025-07-24 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000225s ]
[2025-07-24 12:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000148s ]
[2025-07-24 12:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000145s ]
[2025-07-24 12:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753330201  WHERE (  `id` = 750030 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000120s ]
[2025-07-24 12:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000125s ]
[2025-07-24 12:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000329s ]
[2025-07-24 12:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299530s ]
[2025-07-24 12:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000323s ]
[2025-07-24 12:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000294s ]
[2025-07-24 12:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000315s ]
[2025-07-24 12:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001520s ]
[2025-07-24 12:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000173s ]
[2025-07-24 12:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000202s ]
[2025-07-24 12:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000094s ]
[2025-07-24 12:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-24 12:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-24 12:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000093s ]
[2025-07-24 12:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753330202  WHERE (  `id` = 750031 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-24 12:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000183s ]
[2025-07-24 12:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000190s ]
[2025-07-24 12:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300286s ]
[2025-07-24 12:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000315s ]
[2025-07-24 12:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000296s ]
[2025-07-24 12:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000127s ]
[2025-07-24 12:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001429s ]
[2025-07-24 12:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000211s ]
[2025-07-24 12:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000310s ]
[2025-07-24 12:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000157s ]
[2025-07-24 12:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000151s ]
[2025-07-24 12:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000147s ]
[2025-07-24 12:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000160s ]
[2025-07-24 12:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753330202  WHERE (  `id` = 750032 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-24 12:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000208s ]
[2025-07-24 12:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000143s ]
[2025-07-24 12:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000403s ]
[2025-07-24 12:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000200s ]
[2025-07-24 12:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000184s ]
[2025-07-24 12:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000195s ]
[2025-07-24 12:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300004s ]
[2025-07-24 12:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000219s ]
[2025-07-24 12:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000148s ]
[2025-07-24 12:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-24 12:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001449s ]
[2025-07-24 12:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000119s ]
[2025-07-24 12:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000199s ]
[2025-07-24 12:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000247s ]
[2025-07-24 12:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000158s ]
[2025-07-24 12:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 12:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000099s ]
[2025-07-24 12:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753330202  WHERE (  `id` = 750033 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-24 12:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000328s ]
[2025-07-24 12:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000180s ]
[2025-07-24 12:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000137s ]
[2025-07-24 12:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000120s ]
[2025-07-24 12:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000124s ]
[2025-07-24 12:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299766s ]
[2025-07-24 12:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000219s ]
[2025-07-24 12:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000181s ]
[2025-07-24 12:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000218s ]
[2025-07-24 12:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001485s ]
[2025-07-24 12:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-24 12:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000134s ]
[2025-07-24 12:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000171s ]
[2025-07-24 12:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000094s ]
[2025-07-24 12:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-24 12:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000098s ]
[2025-07-24 12:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753330203  WHERE (  `id` = 750034 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-24 12:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000350s ]
[2025-07-24 12:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000187s ]
[2025-07-24 12:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000197s ]
[2025-07-24 12:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000183s ]
[2025-07-24 12:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000125s ]
[2025-07-24 12:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299887s ]
[2025-07-24 12:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000214s ]
[2025-07-24 12:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000097s ]
[2025-07-24 12:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-24 12:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001435s ]
[2025-07-24 12:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-24 12:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000136s ]
[2025-07-24 12:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000091s ]
[2025-07-24 12:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-24 12:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-24 12:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-24 12:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753330203  WHERE (  `id` = 750035 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-24 12:30:01][sql] CONNECT:[ UseTime:0.000453s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 12:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-24 11' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.007616s ]
[2025-07-24 12:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-24 11' [ RunTime:0.011838s ]
[2025-07-24 12:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004547s ]
[2025-07-24 12:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-24 11' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.001420s ]
[2025-07-24 12:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-24 11' [ RunTime:0.002820s ]
[2025-07-24 13:10:01][sql] CONNECT:[ UseTime:0.000340s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 13:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000311s ]
[2025-07-24 13:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000194s ]
[2025-07-24 13:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000191s ]
[2025-07-24 13:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000154s ]
[2025-07-24 13:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000399s ]
[2025-07-24 13:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002622s ]
[2025-07-24 13:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000173s ]
[2025-07-24 13:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000276s ]
[2025-07-24 13:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000251s ]
[2025-07-24 13:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000251s ]
[2025-07-24 13:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301576s ]
[2025-07-24 13:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000387s ]
[2025-07-24 13:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000175s ]
[2025-07-24 13:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000093s ]
[2025-07-24 13:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000219s ]
[2025-07-24 13:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000185s ]
[2025-07-24 13:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000283s ]
[2025-07-24 13:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001654s ]
[2025-07-24 13:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000184s ]
[2025-07-24 13:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000115s ]
[2025-07-24 13:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000172s ]
[2025-07-24 13:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000142s ]
[2025-07-24 13:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000147s ]
[2025-07-24 13:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000099s ]
[2025-07-24 13:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-24 13:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000144s ]
[2025-07-24 13:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 13:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000092s ]
[2025-07-24 13:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753333801  WHERE (  `id` = 750030 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000117s ]
[2025-07-24 13:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000158s ]
[2025-07-24 13:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000182s ]
[2025-07-24 13:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300262s ]
[2025-07-24 13:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000130s ]
[2025-07-24 13:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-24 13:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-24 13:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001465s ]
[2025-07-24 13:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000157s ]
[2025-07-24 13:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000199s ]
[2025-07-24 13:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000143s ]
[2025-07-24 13:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000143s ]
[2025-07-24 13:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-24 13:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000148s ]
[2025-07-24 13:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753333801  WHERE (  `id` = 750031 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-24 13:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000178s ]
[2025-07-24 13:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000189s ]
[2025-07-24 13:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300280s ]
[2025-07-24 13:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000242s ]
[2025-07-24 13:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000157s ]
[2025-07-24 13:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000185s ]
[2025-07-24 13:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001620s ]
[2025-07-24 13:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000203s ]
[2025-07-24 13:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000176s ]
[2025-07-24 13:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000125s ]
[2025-07-24 13:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000120s ]
[2025-07-24 13:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-24 13:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000133s ]
[2025-07-24 13:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753333802  WHERE (  `id` = 750032 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-24 13:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000233s ]
[2025-07-24 13:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000169s ]
[2025-07-24 13:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000279s ]
[2025-07-24 13:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000178s ]
[2025-07-24 13:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000162s ]
[2025-07-24 13:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000145s ]
[2025-07-24 13:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300920s ]
[2025-07-24 13:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000332s ]
[2025-07-24 13:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000325s ]
[2025-07-24 13:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000315s ]
[2025-07-24 13:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001516s ]
[2025-07-24 13:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-24 13:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000135s ]
[2025-07-24 13:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-24 13:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000152s ]
[2025-07-24 13:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000148s ]
[2025-07-24 13:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000166s ]
[2025-07-24 13:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753333802  WHERE (  `id` = 750033 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000166s ]
[2025-07-24 13:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000333s ]
[2025-07-24 13:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000186s ]
[2025-07-24 13:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000187s ]
[2025-07-24 13:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000176s ]
[2025-07-24 13:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000123s ]
[2025-07-24 13:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300161s ]
[2025-07-24 13:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000290s ]
[2025-07-24 13:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000319s ]
[2025-07-24 13:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000311s ]
[2025-07-24 13:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001597s ]
[2025-07-24 13:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000167s ]
[2025-07-24 13:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000192s ]
[2025-07-24 13:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000093s ]
[2025-07-24 13:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-24 13:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-24 13:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000101s ]
[2025-07-24 13:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753333802  WHERE (  `id` = 750034 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000168s ]
[2025-07-24 13:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000331s ]
[2025-07-24 13:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000250s ]
[2025-07-24 13:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000195s ]
[2025-07-24 13:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000181s ]
[2025-07-24 13:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000179s ]
[2025-07-24 13:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300347s ]
[2025-07-24 13:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000127s ]
[2025-07-24 13:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000237s ]
[2025-07-24 13:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000302s ]
[2025-07-24 13:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001495s ]
[2025-07-24 13:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-24 13:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000134s ]
[2025-07-24 13:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 13:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-24 13:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 13:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000097s ]
[2025-07-24 13:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753333803  WHERE (  `id` = 750035 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-24 13:30:01][sql] CONNECT:[ UseTime:0.000438s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 13:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-24 12' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.007446s ]
[2025-07-24 13:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-24 12' [ RunTime:0.009365s ]
[2025-07-24 13:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004013s ]
[2025-07-24 13:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-24 12' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000393s ]
[2025-07-24 13:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-24 12' [ RunTime:0.003889s ]
[2025-07-24 14:10:01][sql] CONNECT:[ UseTime:0.000295s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 14:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000434s ]
[2025-07-24 14:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000352s ]
[2025-07-24 14:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000389s ]
[2025-07-24 14:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000178s ]
[2025-07-24 14:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000253s ]
[2025-07-24 14:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002544s ]
[2025-07-24 14:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000172s ]
[2025-07-24 14:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000109s ]
[2025-07-24 14:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000130s ]
[2025-07-24 14:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000196s ]
[2025-07-24 14:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300375s ]
[2025-07-24 14:10:02][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000263s ]
[2025-07-24 14:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000168s ]
[2025-07-24 14:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000158s ]
[2025-07-24 14:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000269s ]
[2025-07-24 14:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000271s ]
[2025-07-24 14:10:02][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000296s ]
[2025-07-24 14:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001656s ]
[2025-07-24 14:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000229s ]
[2025-07-24 14:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000168s ]
[2025-07-24 14:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000176s ]
[2025-07-24 14:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000145s ]
[2025-07-24 14:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000320s ]
[2025-07-24 14:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000163s ]
[2025-07-24 14:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000152s ]
[2025-07-24 14:10:02][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000168s ]
[2025-07-24 14:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000093s ]
[2025-07-24 14:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-24 14:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753337402  WHERE (  `id` = 750030 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000117s ]
[2025-07-24 14:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000202s ]
[2025-07-24 14:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000224s ]
[2025-07-24 14:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300192s ]
[2025-07-24 14:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000342s ]
[2025-07-24 14:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000253s ]
[2025-07-24 14:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000331s ]
[2025-07-24 14:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001512s ]
[2025-07-24 14:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000175s ]
[2025-07-24 14:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000201s ]
[2025-07-24 14:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000094s ]
[2025-07-24 14:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-24 14:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-24 14:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000092s ]
[2025-07-24 14:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753337402  WHERE (  `id` = 750031 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000180s ]
[2025-07-24 14:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000274s ]
[2025-07-24 14:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000264s ]
[2025-07-24 14:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299435s ]
[2025-07-24 14:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000243s ]
[2025-07-24 14:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000102s ]
[2025-07-24 14:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-24 14:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001481s ]
[2025-07-24 14:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-24 14:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000135s ]
[2025-07-24 14:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 14:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-24 14:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 14:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-24 14:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753337402  WHERE (  `id` = 750032 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-24 14:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000259s ]
[2025-07-24 14:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000296s ]
[2025-07-24 14:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000413s ]
[2025-07-24 14:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000279s ]
[2025-07-24 14:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000184s ]
[2025-07-24 14:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000182s ]
[2025-07-24 14:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301271s ]
[2025-07-24 14:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000175s ]
[2025-07-24 14:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000092s ]
[2025-07-24 14:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-24 14:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001536s ]
[2025-07-24 14:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000164s ]
[2025-07-24 14:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000193s ]
[2025-07-24 14:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000146s ]
[2025-07-24 14:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000142s ]
[2025-07-24 14:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-24 14:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000100s ]
[2025-07-24 14:10:03][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753337403  WHERE (  `id` = 750033 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000115s ]
[2025-07-24 14:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000340s ]
[2025-07-24 14:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000184s ]
[2025-07-24 14:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000184s ]
[2025-07-24 14:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000176s ]
[2025-07-24 14:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000175s ]
[2025-07-24 14:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.305767s ]
[2025-07-24 14:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000355s ]
[2025-07-24 14:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000186s ]
[2025-07-24 14:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000122s ]
[2025-07-24 14:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001516s ]
[2025-07-24 14:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000266s ]
[2025-07-24 14:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000216s ]
[2025-07-24 14:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000097s ]
[2025-07-24 14:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 14:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-24 14:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000097s ]
[2025-07-24 14:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753337403  WHERE (  `id` = 750034 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000120s ]
[2025-07-24 14:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000379s ]
[2025-07-24 14:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000310s ]
[2025-07-24 14:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000286s ]
[2025-07-24 14:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000214s ]
[2025-07-24 14:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000182s ]
[2025-07-24 14:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301232s ]
[2025-07-24 14:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000283s ]
[2025-07-24 14:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000170s ]
[2025-07-24 14:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000115s ]
[2025-07-24 14:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001451s ]
[2025-07-24 14:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000189s ]
[2025-07-24 14:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000146s ]
[2025-07-24 14:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000092s ]
[2025-07-24 14:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-24 14:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-24 14:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000098s ]
[2025-07-24 14:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753337403  WHERE (  `id` = 750035 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000106s ]
[2025-07-24 14:30:01][sql] CONNECT:[ UseTime:0.000432s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 14:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-24 13' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.006657s ]
[2025-07-24 14:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-24 13' [ RunTime:0.009021s ]
[2025-07-24 14:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004467s ]
[2025-07-24 14:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-24 13' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000608s ]
[2025-07-24 14:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-24 13' [ RunTime:0.004135s ]
[2025-07-24 15:10:01][sql] CONNECT:[ UseTime:0.000433s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 15:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000498s ]
[2025-07-24 15:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000252s ]
[2025-07-24 15:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000293s ]
[2025-07-24 15:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000242s ]
[2025-07-24 15:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000311s ]
[2025-07-24 15:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002605s ]
[2025-07-24 15:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000221s ]
[2025-07-24 15:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000115s ]
[2025-07-24 15:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000132s ]
[2025-07-24 15:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000227s ]
[2025-07-24 15:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299965s ]
[2025-07-24 15:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000266s ]
[2025-07-24 15:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000282s ]
[2025-07-24 15:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000314s ]
[2025-07-24 15:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000339s ]
[2025-07-24 15:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000181s ]
[2025-07-24 15:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000233s ]
[2025-07-24 15:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001524s ]
[2025-07-24 15:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000221s ]
[2025-07-24 15:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000156s ]
[2025-07-24 15:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000163s ]
[2025-07-24 15:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000177s ]
[2025-07-24 15:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000152s ]
[2025-07-24 15:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000095s ]
[2025-07-24 15:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-24 15:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000149s ]
[2025-07-24 15:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 15:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000092s ]
[2025-07-24 15:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753341001  WHERE (  `id` = 750030 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-24 15:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000111s ]
[2025-07-24 15:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000343s ]
[2025-07-24 15:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301099s ]
[2025-07-24 15:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000250s ]
[2025-07-24 15:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000132s ]
[2025-07-24 15:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000171s ]
[2025-07-24 15:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001525s ]
[2025-07-24 15:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000235s ]
[2025-07-24 15:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000153s ]
[2025-07-24 15:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000097s ]
[2025-07-24 15:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 15:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-24 15:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-24 15:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753341001  WHERE (  `id` = 750031 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000128s ]
[2025-07-24 15:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000139s ]
[2025-07-24 15:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000233s ]
[2025-07-24 15:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299801s ]
[2025-07-24 15:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000184s ]
[2025-07-24 15:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000097s ]
[2025-07-24 15:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000119s ]
[2025-07-24 15:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001533s ]
[2025-07-24 15:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000201s ]
[2025-07-24 15:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000225s ]
[2025-07-24 15:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000099s ]
[2025-07-24 15:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 15:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-24 15:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000100s ]
[2025-07-24 15:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753341002  WHERE (  `id` = 750032 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-24 15:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000261s ]
[2025-07-24 15:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000132s ]
[2025-07-24 15:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000234s ]
[2025-07-24 15:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000145s ]
[2025-07-24 15:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000123s ]
[2025-07-24 15:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000117s ]
[2025-07-24 15:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299598s ]
[2025-07-24 15:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000217s ]
[2025-07-24 15:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000187s ]
[2025-07-24 15:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000158s ]
[2025-07-24 15:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001425s ]
[2025-07-24 15:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-24 15:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000133s ]
[2025-07-24 15:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000091s ]
[2025-07-24 15:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-24 15:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-24 15:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-24 15:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753341002  WHERE (  `id` = 750033 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000106s ]
[2025-07-24 15:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000340s ]
[2025-07-24 15:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000193s ]
[2025-07-24 15:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000185s ]
[2025-07-24 15:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000176s ]
[2025-07-24 15:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000122s ]
[2025-07-24 15:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299335s ]
[2025-07-24 15:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000212s ]
[2025-07-24 15:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-24 15:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-24 15:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001447s ]
[2025-07-24 15:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000189s ]
[2025-07-24 15:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000186s ]
[2025-07-24 15:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000142s ]
[2025-07-24 15:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 15:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-24 15:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-24 15:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753341002  WHERE (  `id` = 750034 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-24 15:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000302s ]
[2025-07-24 15:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000254s ]
[2025-07-24 15:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000209s ]
[2025-07-24 15:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000182s ]
[2025-07-24 15:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000177s ]
[2025-07-24 15:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299545s ]
[2025-07-24 15:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000226s ]
[2025-07-24 15:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-24 15:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-24 15:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001436s ]
[2025-07-24 15:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000105s ]
[2025-07-24 15:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000136s ]
[2025-07-24 15:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-24 15:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000167s ]
[2025-07-24 15:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000148s ]
[2025-07-24 15:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000207s ]
[2025-07-24 15:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753341003  WHERE (  `id` = 750035 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000175s ]
[2025-07-24 15:30:01][sql] CONNECT:[ UseTime:0.000419s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 15:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-24 14' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.007331s ]
[2025-07-24 15:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-24 14' [ RunTime:0.011171s ]
[2025-07-24 15:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004307s ]
[2025-07-24 15:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-24 14' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000858s ]
[2025-07-24 15:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-24 14' [ RunTime:0.002702s ]
[2025-07-24 16:10:01][sql] CONNECT:[ UseTime:0.000430s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 16:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000509s ]
[2025-07-24 16:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000355s ]
[2025-07-24 16:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000248s ]
[2025-07-24 16:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000173s ]
[2025-07-24 16:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000277s ]
[2025-07-24 16:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002682s ]
[2025-07-24 16:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000266s ]
[2025-07-24 16:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000120s ]
[2025-07-24 16:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000132s ]
[2025-07-24 16:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000201s ]
[2025-07-24 16:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299784s ]
[2025-07-24 16:10:02][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000326s ]
[2025-07-24 16:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000317s ]
[2025-07-24 16:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000308s ]
[2025-07-24 16:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000234s ]
[2025-07-24 16:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000181s ]
[2025-07-24 16:10:02][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000234s ]
[2025-07-24 16:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001488s ]
[2025-07-24 16:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000170s ]
[2025-07-24 16:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000115s ]
[2025-07-24 16:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000348s ]
[2025-07-24 16:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000159s ]
[2025-07-24 16:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000203s ]
[2025-07-24 16:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000096s ]
[2025-07-24 16:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-24 16:10:02][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000149s ]
[2025-07-24 16:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 16:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000090s ]
[2025-07-24 16:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753344602  WHERE (  `id` = 750030 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-24 16:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000108s ]
[2025-07-24 16:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000128s ]
[2025-07-24 16:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300009s ]
[2025-07-24 16:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000139s ]
[2025-07-24 16:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000093s ]
[2025-07-24 16:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000332s ]
[2025-07-24 16:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001550s ]
[2025-07-24 16:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000115s ]
[2025-07-24 16:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000135s ]
[2025-07-24 16:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 16:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000100s ]
[2025-07-24 16:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-24 16:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000089s ]
[2025-07-24 16:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753344602  WHERE (  `id` = 750031 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-24 16:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000185s ]
[2025-07-24 16:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000133s ]
[2025-07-24 16:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.303686s ]
[2025-07-24 16:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000381s ]
[2025-07-24 16:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000333s ]
[2025-07-24 16:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000332s ]
[2025-07-24 16:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001541s ]
[2025-07-24 16:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000179s ]
[2025-07-24 16:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000204s ]
[2025-07-24 16:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000163s ]
[2025-07-24 16:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000154s ]
[2025-07-24 16:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000150s ]
[2025-07-24 16:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000164s ]
[2025-07-24 16:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753344602  WHERE (  `id` = 750032 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000220s ]
[2025-07-24 16:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000255s ]
[2025-07-24 16:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000200s ]
[2025-07-24 16:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000281s ]
[2025-07-24 16:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000193s ]
[2025-07-24 16:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000123s ]
[2025-07-24 16:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000118s ]
[2025-07-24 16:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299529s ]
[2025-07-24 16:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000126s ]
[2025-07-24 16:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000092s ]
[2025-07-24 16:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-24 16:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001433s ]
[2025-07-24 16:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000103s ]
[2025-07-24 16:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000133s ]
[2025-07-24 16:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-24 16:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-24 16:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 16:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-24 16:10:03][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753344603  WHERE (  `id` = 750033 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000105s ]
[2025-07-24 16:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000401s ]
[2025-07-24 16:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000313s ]
[2025-07-24 16:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000270s ]
[2025-07-24 16:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000181s ]
[2025-07-24 16:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000176s ]
[2025-07-24 16:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300255s ]
[2025-07-24 16:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000362s ]
[2025-07-24 16:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000153s ]
[2025-07-24 16:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000117s ]
[2025-07-24 16:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001493s ]
[2025-07-24 16:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-24 16:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-24 16:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-24 16:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-24 16:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-24 16:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000092s ]
[2025-07-24 16:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753344603  WHERE (  `id` = 750034 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000116s ]
[2025-07-24 16:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000342s ]
[2025-07-24 16:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000258s ]
[2025-07-24 16:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000200s ]
[2025-07-24 16:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000179s ]
[2025-07-24 16:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-24 00:00:00' AND '2025-07-24 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000179s ]
[2025-07-24 16:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753286400 AND 1753372799 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299630s ]
[2025-07-24 16:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000249s ]
[2025-07-24 16:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000101s ]
[2025-07-24 16:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-24 16:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001457s ]
[2025-07-24 16:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000154s ]
[2025-07-24 16:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753372799 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000138s ]
[2025-07-24 16:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000091s ]
[2025-07-24 16:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-24 16:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753372799 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-24 16:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-24 16:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753344603  WHERE (  `id` = 750035 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000106s ]
[2025-07-24 16:30:01][sql] CONNECT:[ UseTime:0.000438s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-24 16:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-24 15' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.007592s ]
[2025-07-24 16:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-24 15' [ RunTime:0.011621s ]
[2025-07-24 16:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.003976s ]
[2025-07-24 16:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-24 15' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000312s ]
[2025-07-24 16:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-24 15' [ RunTime:0.002100s ]
