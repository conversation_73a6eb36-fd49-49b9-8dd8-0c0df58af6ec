[2025-07-16 15:10:02][sql] CONNECT:[ UseTime:0.000350s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-16 15:10:02][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000328s ]
[2025-07-16 15:10:02][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000768s ]
[2025-07-16 15:10:02][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000425s ]
[2025-07-16 15:10:02][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000242s ]
[2025-07-16 15:10:02][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000276s ]
[2025-07-16 15:10:02][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002615s ]
[2025-07-16 15:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000225s ]
[2025-07-16 15:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000163s ]
[2025-07-16 15:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000191s ]
[2025-07-16 15:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000249s ]
[2025-07-16 15:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000190s ]
[2025-07-16 15:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000166s ]
[2025-07-16 15:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000106s ]
[2025-07-16 15:10:02][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000211s ]
[2025-07-16 15:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.307519s ]
[2025-07-16 15:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000254s ]
[2025-07-16 15:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000368s ]
[2025-07-16 15:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000302s ]
[2025-07-16 15:10:02][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000221s ]
[2025-07-16 15:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001503s ]
[2025-07-16 15:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000211s ]
[2025-07-16 15:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000159s ]
[2025-07-16 15:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000172s ]
[2025-07-16 15:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000141s ]
[2025-07-16 15:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000313s ]
[2025-07-16 15:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000149s ]
[2025-07-16 15:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000148s ]
[2025-07-16 15:10:02][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000208s ]
[2025-07-16 15:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000141s ]
[2025-07-16 15:10:02][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000232s ]
[2025-07-16 15:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000150s ]
[2025-07-16 15:10:02][sql] INSERT INTO `ea_breed_data` SET `farmid` = '0216' , `batch_id` = 47 , `prod_id` = 897 , `prod_date` = 1752595200 , `cunlan` = 0 , `days` = 20286 [ RunTime:0.004513s ]
[2025-07-16 15:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000155s ]
[2025-07-16 15:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000249s ]
[2025-07-16 15:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000168s ]
[2025-07-16 15:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000103s ]
[2025-07-16 15:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000098s ]
[2025-07-16 15:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.306176s ]
[2025-07-16 15:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000257s ]
[2025-07-16 15:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000186s ]
[2025-07-16 15:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001489s ]
[2025-07-16 15:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000173s ]
[2025-07-16 15:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000199s ]
[2025-07-16 15:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000159s ]
[2025-07-16 15:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000149s ]
[2025-07-16 15:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000145s ]
[2025-07-16 15:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000215s ]
[2025-07-16 15:10:02][sql] INSERT INTO `ea_breed_data` SET `farmid` = '0216' , `batch_id` = 24 , `prod_id` = 872 , `prod_date` = 1752595200 , `cunlan` = 0 , `days` = 20286 [ RunTime:0.004832s ]
[2025-07-16 15:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000212s ]
[2025-07-16 15:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000257s ]
[2025-07-16 15:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.006992s ]
[2025-07-16 15:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000171s ]
[2025-07-16 15:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000165s ]
[2025-07-16 15:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.306516s ]
[2025-07-16 15:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000301s ]
[2025-07-16 15:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000319s ]
[2025-07-16 15:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001515s ]
[2025-07-16 15:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000274s ]
[2025-07-16 15:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000309s ]
[2025-07-16 15:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000232s ]
[2025-07-16 15:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000165s ]
[2025-07-16 15:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000167s ]
[2025-07-16 15:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000184s ]
[2025-07-16 15:10:03][sql] INSERT INTO `ea_breed_data` SET `farmid` = '0216' , `batch_id` = 157 , `prod_id` = 1030 , `prod_date` = 1752595200 , `cunlan` = 37331 , `days` = 443 [ RunTime:0.005565s ]
[2025-07-16 15:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000350s ]
[2025-07-16 15:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000278s ]
[2025-07-16 15:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.306458s ]
[2025-07-16 15:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000165s ]
[2025-07-16 15:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000116s ]
[2025-07-16 15:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001469s ]
[2025-07-16 15:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000193s ]
[2025-07-16 15:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000222s ]
[2025-07-16 15:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000138s ]
[2025-07-16 15:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-16 15:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-16 15:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000098s ]
[2025-07-16 15:10:03][sql] INSERT INTO `ea_breed_data` SET `farmid` = '0216' , `batch_id` = 460 , `prod_id` = 1332 , `prod_date` = 1752595200 , `cunlan` = 17996 , `days` = 364 [ RunTime:0.005163s ]
[2025-07-16 15:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000379s ]
[2025-07-16 15:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000310s ]
[2025-07-16 15:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000197s ]
[2025-07-16 15:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000166s ]
[2025-07-16 15:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000159s ]
[2025-07-16 15:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.306104s ]
[2025-07-16 15:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000125s ]
[2025-07-16 15:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000153s ]
[2025-07-16 15:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001466s ]
[2025-07-16 15:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000153s ]
[2025-07-16 15:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-16 15:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-16 15:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-16 15:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-16 15:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000097s ]
[2025-07-16 15:10:03][sql] INSERT INTO `ea_breed_data` SET `farmid` = '0216' , `batch_id` = 844 , `prod_id` = 1724 , `prod_date` = 1752595200 , `cunlan` = 0 , `days` = 20286 [ RunTime:0.004911s ]
[2025-07-16 15:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000295s ]
[2025-07-16 15:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000291s ]
[2025-07-16 15:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.002840s ]
[2025-07-16 15:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000129s ]
[2025-07-16 15:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000107s ]
[2025-07-16 15:10:04][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.307087s ]
[2025-07-16 15:10:04][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000204s ]
[2025-07-16 15:10:04][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000117s ]
[2025-07-16 15:10:04][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001451s ]
[2025-07-16 15:10:04][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000193s ]
[2025-07-16 15:10:04][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000311s ]
[2025-07-16 15:10:04][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000180s ]
[2025-07-16 15:10:04][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000151s ]
[2025-07-16 15:10:04][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000149s ]
[2025-07-16 15:10:04][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000120s ]
[2025-07-16 15:10:04][sql] INSERT INTO `ea_breed_data` SET `farmid` = '0216' , `batch_id` = 843 , `prod_id` = 1723 , `prod_date` = 1752595200 , `cunlan` = 0 , `days` = 20286 [ RunTime:0.005091s ]
[2025-07-16 15:30:01][sql] CONNECT:[ UseTime:0.000488s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-16 15:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_realtime where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-16 14' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.010612s ]
[2025-07-16 15:30:01][sql] delete from ea_hjxx_realtime where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-16 14' [ RunTime:0.019877s ]
[2025-07-16 15:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004286s ]
[2025-07-16 15:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-16 14' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000759s ]
[2025-07-16 15:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-16 14' [ RunTime:0.004569s ]
[2025-07-16 16:10:01][sql] CONNECT:[ UseTime:0.000460s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-16 16:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000502s ]
[2025-07-16 16:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000419s ]
[2025-07-16 16:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000240s ]
[2025-07-16 16:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000172s ]
[2025-07-16 16:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000277s ]
[2025-07-16 16:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002612s ]
[2025-07-16 16:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000231s ]
[2025-07-16 16:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000113s ]
[2025-07-16 16:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000136s ]
[2025-07-16 16:10:01][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000196s ]
[2025-07-16 16:10:01][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000116s ]
[2025-07-16 16:10:01][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000102s ]
[2025-07-16 16:10:01][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000093s ]
[2025-07-16 16:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000195s ]
[2025-07-16 16:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300695s ]
[2025-07-16 16:10:02][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000407s ]
[2025-07-16 16:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000280s ]
[2025-07-16 16:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000181s ]
[2025-07-16 16:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000228s ]
[2025-07-16 16:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000181s ]
[2025-07-16 16:10:02][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000228s ]
[2025-07-16 16:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001507s ]
[2025-07-16 16:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000219s ]
[2025-07-16 16:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000168s ]
[2025-07-16 16:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000217s ]
[2025-07-16 16:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000196s ]
[2025-07-16 16:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000210s ]
[2025-07-16 16:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000151s ]
[2025-07-16 16:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000145s ]
[2025-07-16 16:10:02][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000213s ]
[2025-07-16 16:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000098s ]
[2025-07-16 16:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-16 16:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752653402  WHERE (  `id` = 749982 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000200s ]
[2025-07-16 16:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000288s ]
[2025-07-16 16:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000305s ]
[2025-07-16 16:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000246s ]
[2025-07-16 16:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000160s ]
[2025-07-16 16:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000149s ]
[2025-07-16 16:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300047s ]
[2025-07-16 16:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000156s ]
[2025-07-16 16:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-16 16:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-16 16:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001441s ]
[2025-07-16 16:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-16 16:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000133s ]
[2025-07-16 16:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000124s ]
[2025-07-16 16:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000093s ]
[2025-07-16 16:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-16 16:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000089s ]
[2025-07-16 16:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752653402  WHERE (  `id` = 749983 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000189s ]
[2025-07-16 16:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000132s ]
[2025-07-16 16:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000205s ]
[2025-07-16 16:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000122s ]
[2025-07-16 16:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000100s ]
[2025-07-16 16:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000100s ]
[2025-07-16 16:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299397s ]
[2025-07-16 16:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000286s ]
[2025-07-16 16:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000255s ]
[2025-07-16 16:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000202s ]
[2025-07-16 16:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001464s ]
[2025-07-16 16:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-16 16:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000133s ]
[2025-07-16 16:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-16 16:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-16 16:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-16 16:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000099s ]
[2025-07-16 16:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752653402  WHERE (  `id` = 749984 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000158s ]
[2025-07-16 16:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000248s ]
[2025-07-16 16:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000192s ]
[2025-07-16 16:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300369s ]
[2025-07-16 16:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000272s ]
[2025-07-16 16:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000117s ]
[2025-07-16 16:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000119s ]
[2025-07-16 16:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001494s ]
[2025-07-16 16:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000167s ]
[2025-07-16 16:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-16 16:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-16 16:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-16 16:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-16 16:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000097s ]
[2025-07-16 16:10:03][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752653403  WHERE (  `id` = 749985 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000122s ]
[2025-07-16 16:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000294s ]
[2025-07-16 16:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000279s ]
[2025-07-16 16:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000170s ]
[2025-07-16 16:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000102s ]
[2025-07-16 16:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000098s ]
[2025-07-16 16:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299344s ]
[2025-07-16 16:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000289s ]
[2025-07-16 16:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000313s ]
[2025-07-16 16:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000271s ]
[2025-07-16 16:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001502s ]
[2025-07-16 16:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000166s ]
[2025-07-16 16:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-16 16:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000093s ]
[2025-07-16 16:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-16 16:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000147s ]
[2025-07-16 16:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000166s ]
[2025-07-16 16:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752653403  WHERE (  `id` = 749986 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000179s ]
[2025-07-16 16:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000374s ]
[2025-07-16 16:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000284s ]
[2025-07-16 16:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000192s ]
[2025-07-16 16:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000156s ]
[2025-07-16 16:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000153s ]
[2025-07-16 16:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299508s ]
[2025-07-16 16:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000285s ]
[2025-07-16 16:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000242s ]
[2025-07-16 16:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000183s ]
[2025-07-16 16:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001455s ]
[2025-07-16 16:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000191s ]
[2025-07-16 16:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000221s ]
[2025-07-16 16:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000098s ]
[2025-07-16 16:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-16 16:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-16 16:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000098s ]
[2025-07-16 16:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752653403  WHERE (  `id` = 749987 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000117s ]
[2025-07-16 16:30:01][sql] CONNECT:[ UseTime:0.000401s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-16 16:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_realtime where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-16 15' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.005998s ]
[2025-07-16 16:30:01][sql] delete from ea_hjxx_realtime where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-16 15' [ RunTime:0.005217s ]
[2025-07-16 16:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004809s ]
[2025-07-16 16:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-16 15' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000730s ]
[2025-07-16 16:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-16 15' [ RunTime:0.002152s ]
[2025-07-16 17:10:01][sql] CONNECT:[ UseTime:0.000513s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-16 17:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000527s ]
[2025-07-16 17:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000289s ]
[2025-07-16 17:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000257s ]
[2025-07-16 17:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000128s ]
[2025-07-16 17:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000445s ]
[2025-07-16 17:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002735s ]
[2025-07-16 17:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000227s ]
[2025-07-16 17:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000199s ]
[2025-07-16 17:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000191s ]
[2025-07-16 17:10:01][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000252s ]
[2025-07-16 17:10:01][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000174s ]
[2025-07-16 17:10:01][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000155s ]
[2025-07-16 17:10:01][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000153s ]
[2025-07-16 17:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000256s ]
[2025-07-16 17:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299365s ]
[2025-07-16 17:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000402s ]
[2025-07-16 17:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000298s ]
[2025-07-16 17:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000241s ]
[2025-07-16 17:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000228s ]
[2025-07-16 17:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000174s ]
[2025-07-16 17:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000241s ]
[2025-07-16 17:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001484s ]
[2025-07-16 17:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000216s ]
[2025-07-16 17:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000164s ]
[2025-07-16 17:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000221s ]
[2025-07-16 17:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000195s ]
[2025-07-16 17:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000211s ]
[2025-07-16 17:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000145s ]
[2025-07-16 17:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000143s ]
[2025-07-16 17:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000167s ]
[2025-07-16 17:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000092s ]
[2025-07-16 17:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000092s ]
[2025-07-16 17:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752657001  WHERE (  `id` = 749982 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000121s ]
[2025-07-16 17:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000160s ]
[2025-07-16 17:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000197s ]
[2025-07-16 17:10:01][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000165s ]
[2025-07-16 17:10:01][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000152s ]
[2025-07-16 17:10:01][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000151s ]
[2025-07-16 17:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301936s ]
[2025-07-16 17:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000429s ]
[2025-07-16 17:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000241s ]
[2025-07-16 17:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000219s ]
[2025-07-16 17:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001513s ]
[2025-07-16 17:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000115s ]
[2025-07-16 17:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000140s ]
[2025-07-16 17:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000091s ]
[2025-07-16 17:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-16 17:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-16 17:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000165s ]
[2025-07-16 17:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752657001  WHERE (  `id` = 749983 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000178s ]
[2025-07-16 17:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000182s ]
[2025-07-16 17:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000255s ]
[2025-07-16 17:10:01][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000165s ]
[2025-07-16 17:10:01][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000153s ]
[2025-07-16 17:10:01][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000152s ]
[2025-07-16 17:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302099s ]
[2025-07-16 17:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000268s ]
[2025-07-16 17:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000231s ]
[2025-07-16 17:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000252s ]
[2025-07-16 17:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001551s ]
[2025-07-16 17:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-16 17:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000136s ]
[2025-07-16 17:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000124s ]
[2025-07-16 17:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-16 17:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-16 17:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-16 17:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752657002  WHERE (  `id` = 749984 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000126s ]
[2025-07-16 17:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000199s ]
[2025-07-16 17:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000137s ]
[2025-07-16 17:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299299s ]
[2025-07-16 17:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000215s ]
[2025-07-16 17:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000141s ]
[2025-07-16 17:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-16 17:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001412s ]
[2025-07-16 17:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000102s ]
[2025-07-16 17:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000214s ]
[2025-07-16 17:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000166s ]
[2025-07-16 17:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000098s ]
[2025-07-16 17:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-16 17:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000100s ]
[2025-07-16 17:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752657002  WHERE (  `id` = 749985 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-16 17:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000337s ]
[2025-07-16 17:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000195s ]
[2025-07-16 17:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000118s ]
[2025-07-16 17:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000100s ]
[2025-07-16 17:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000101s ]
[2025-07-16 17:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300204s ]
[2025-07-16 17:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000305s ]
[2025-07-16 17:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000242s ]
[2025-07-16 17:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000203s ]
[2025-07-16 17:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001540s ]
[2025-07-16 17:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000168s ]
[2025-07-16 17:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000142s ]
[2025-07-16 17:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000092s ]
[2025-07-16 17:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-16 17:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-16 17:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-16 17:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752657002  WHERE (  `id` = 749986 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000117s ]
[2025-07-16 17:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000476s ]
[2025-07-16 17:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000261s ]
[2025-07-16 17:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000169s ]
[2025-07-16 17:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000160s ]
[2025-07-16 17:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000153s ]
[2025-07-16 17:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299706s ]
[2025-07-16 17:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000338s ]
[2025-07-16 17:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000217s ]
[2025-07-16 17:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000116s ]
[2025-07-16 17:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001447s ]
[2025-07-16 17:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-16 17:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000133s ]
[2025-07-16 17:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-16 17:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-16 17:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-16 17:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-16 17:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752657003  WHERE (  `id` = 749987 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-16 17:30:01][sql] CONNECT:[ UseTime:0.000464s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-16 17:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_realtime where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-16 16' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.001717s ]
[2025-07-16 17:30:01][sql] delete from ea_hjxx_realtime where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-16 16' [ RunTime:0.004161s ]
[2025-07-16 17:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.002192s ]
[2025-07-16 17:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-16 16' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000457s ]
[2025-07-16 17:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-16 16' [ RunTime:0.001961s ]
[2025-07-16 18:10:01][sql] CONNECT:[ UseTime:0.000349s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-16 18:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000572s ]
[2025-07-16 18:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000363s ]
[2025-07-16 18:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000374s ]
[2025-07-16 18:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000218s ]
[2025-07-16 18:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000264s ]
[2025-07-16 18:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002492s ]
[2025-07-16 18:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000211s ]
[2025-07-16 18:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000110s ]
[2025-07-16 18:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000136s ]
[2025-07-16 18:10:01][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000200s ]
[2025-07-16 18:10:01][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000115s ]
[2025-07-16 18:10:01][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000097s ]
[2025-07-16 18:10:01][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000093s ]
[2025-07-16 18:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000196s ]
[2025-07-16 18:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.307164s ]
[2025-07-16 18:10:02][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000427s ]
[2025-07-16 18:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000244s ]
[2025-07-16 18:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000172s ]
[2025-07-16 18:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000240s ]
[2025-07-16 18:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000266s ]
[2025-07-16 18:10:02][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000380s ]
[2025-07-16 18:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001617s ]
[2025-07-16 18:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000228s ]
[2025-07-16 18:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000128s ]
[2025-07-16 18:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000227s ]
[2025-07-16 18:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000151s ]
[2025-07-16 18:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000152s ]
[2025-07-16 18:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000252s ]
[2025-07-16 18:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000153s ]
[2025-07-16 18:10:02][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000219s ]
[2025-07-16 18:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000149s ]
[2025-07-16 18:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000148s ]
[2025-07-16 18:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752660602  WHERE (  `id` = 749982 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000175s ]
[2025-07-16 18:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000162s ]
[2025-07-16 18:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000195s ]
[2025-07-16 18:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000112s ]
[2025-07-16 18:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000097s ]
[2025-07-16 18:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000093s ]
[2025-07-16 18:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.307466s ]
[2025-07-16 18:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000203s ]
[2025-07-16 18:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000261s ]
[2025-07-16 18:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000349s ]
[2025-07-16 18:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001653s ]
[2025-07-16 18:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000166s ]
[2025-07-16 18:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000197s ]
[2025-07-16 18:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000147s ]
[2025-07-16 18:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000144s ]
[2025-07-16 18:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000094s ]
[2025-07-16 18:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000152s ]
[2025-07-16 18:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752660602  WHERE (  `id` = 749983 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000188s ]
[2025-07-16 18:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000306s ]
[2025-07-16 18:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000262s ]
[2025-07-16 18:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000176s ]
[2025-07-16 18:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000156s ]
[2025-07-16 18:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000153s ]
[2025-07-16 18:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.307519s ]
[2025-07-16 18:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000343s ]
[2025-07-16 18:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000177s ]
[2025-07-16 18:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000120s ]
[2025-07-16 18:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001508s ]
[2025-07-16 18:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000259s ]
[2025-07-16 18:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000245s ]
[2025-07-16 18:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000102s ]
[2025-07-16 18:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-16 18:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-16 18:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000099s ]
[2025-07-16 18:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752660602  WHERE (  `id` = 749984 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000117s ]
[2025-07-16 18:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000428s ]
[2025-07-16 18:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000271s ]
[2025-07-16 18:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.307574s ]
[2025-07-16 18:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000317s ]
[2025-07-16 18:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000296s ]
[2025-07-16 18:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000259s ]
[2025-07-16 18:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001445s ]
[2025-07-16 18:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000106s ]
[2025-07-16 18:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000136s ]
[2025-07-16 18:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000169s ]
[2025-07-16 18:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000190s ]
[2025-07-16 18:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000140s ]
[2025-07-16 18:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000162s ]
[2025-07-16 18:10:03][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752660603  WHERE (  `id` = 749985 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000176s ]
[2025-07-16 18:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000331s ]
[2025-07-16 18:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000193s ]
[2025-07-16 18:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000176s ]
[2025-07-16 18:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000157s ]
[2025-07-16 18:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000159s ]
[2025-07-16 18:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.307829s ]
[2025-07-16 18:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000275s ]
[2025-07-16 18:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000100s ]
[2025-07-16 18:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-16 18:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001450s ]
[2025-07-16 18:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000157s ]
[2025-07-16 18:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-16 18:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-16 18:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-16 18:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-16 18:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000093s ]
[2025-07-16 18:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752660603  WHERE (  `id` = 749986 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000116s ]
[2025-07-16 18:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000334s ]
[2025-07-16 18:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000137s ]
[2025-07-16 18:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000112s ]
[2025-07-16 18:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000096s ]
[2025-07-16 18:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000169s ]
[2025-07-16 18:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.307759s ]
[2025-07-16 18:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000259s ]
[2025-07-16 18:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000167s ]
[2025-07-16 18:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000325s ]
[2025-07-16 18:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001629s ]
[2025-07-16 18:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000172s ]
[2025-07-16 18:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000202s ]
[2025-07-16 18:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000094s ]
[2025-07-16 18:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-16 18:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-16 18:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000102s ]
[2025-07-16 18:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752660603  WHERE (  `id` = 749987 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-16 18:30:01][sql] CONNECT:[ UseTime:0.000260s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-16 18:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-16 17' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.005136s ]
[2025-07-16 18:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-16 17' [ RunTime:0.005679s ]
[2025-07-16 18:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.003122s ]
[2025-07-16 18:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-16 17' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000423s ]
[2025-07-16 18:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-16 17' [ RunTime:0.004102s ]
[2025-07-16 19:10:01][sql] CONNECT:[ UseTime:0.000396s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-16 19:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000450s ]
[2025-07-16 19:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000388s ]
[2025-07-16 19:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000246s ]
[2025-07-16 19:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000403s ]
[2025-07-16 19:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000456s ]
[2025-07-16 19:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002627s ]
[2025-07-16 19:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000264s ]
[2025-07-16 19:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000116s ]
[2025-07-16 19:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000416s ]
[2025-07-16 19:10:01][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000200s ]
[2025-07-16 19:10:01][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000119s ]
[2025-07-16 19:10:01][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000097s ]
[2025-07-16 19:10:01][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000094s ]
[2025-07-16 19:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000200s ]
[2025-07-16 19:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300580s ]
[2025-07-16 19:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000298s ]
[2025-07-16 19:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000175s ]
[2025-07-16 19:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000329s ]
[2025-07-16 19:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000343s ]
[2025-07-16 19:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000178s ]
[2025-07-16 19:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000234s ]
[2025-07-16 19:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001544s ]
[2025-07-16 19:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000235s ]
[2025-07-16 19:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000116s ]
[2025-07-16 19:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000159s ]
[2025-07-16 19:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000141s ]
[2025-07-16 19:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000222s ]
[2025-07-16 19:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000151s ]
[2025-07-16 19:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-16 19:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000164s ]
[2025-07-16 19:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000092s ]
[2025-07-16 19:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000093s ]
[2025-07-16 19:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752664201  WHERE (  `id` = 749982 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000120s ]
[2025-07-16 19:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000110s ]
[2025-07-16 19:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000136s ]
[2025-07-16 19:10:01][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000113s ]
[2025-07-16 19:10:01][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000101s ]
[2025-07-16 19:10:01][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000091s ]
[2025-07-16 19:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299878s ]
[2025-07-16 19:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000270s ]
[2025-07-16 19:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000294s ]
[2025-07-16 19:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000320s ]
[2025-07-16 19:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001669s ]
[2025-07-16 19:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000138s ]
[2025-07-16 19:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000203s ]
[2025-07-16 19:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000152s ]
[2025-07-16 19:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000150s ]
[2025-07-16 19:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000104s ]
[2025-07-16 19:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000090s ]
[2025-07-16 19:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752664202  WHERE (  `id` = 749983 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000175s ]
[2025-07-16 19:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000236s ]
[2025-07-16 19:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000258s ]
[2025-07-16 19:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000167s ]
[2025-07-16 19:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000161s ]
[2025-07-16 19:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000152s ]
[2025-07-16 19:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299103s ]
[2025-07-16 19:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000291s ]
[2025-07-16 19:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000165s ]
[2025-07-16 19:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000179s ]
[2025-07-16 19:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001481s ]
[2025-07-16 19:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-16 19:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000134s ]
[2025-07-16 19:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000198s ]
[2025-07-16 19:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000092s ]
[2025-07-16 19:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000166s ]
[2025-07-16 19:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000200s ]
[2025-07-16 19:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752664202  WHERE (  `id` = 749984 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000181s ]
[2025-07-16 19:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000256s ]
[2025-07-16 19:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000251s ]
[2025-07-16 19:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302148s ]
[2025-07-16 19:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000326s ]
[2025-07-16 19:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000163s ]
[2025-07-16 19:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000179s ]
[2025-07-16 19:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001572s ]
[2025-07-16 19:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000156s ]
[2025-07-16 19:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000138s ]
[2025-07-16 19:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-16 19:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-16 19:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-16 19:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000097s ]
[2025-07-16 19:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752664202  WHERE (  `id` = 749985 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000121s ]
[2025-07-16 19:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000338s ]
[2025-07-16 19:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000262s ]
[2025-07-16 19:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000133s ]
[2025-07-16 19:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000099s ]
[2025-07-16 19:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000095s ]
[2025-07-16 19:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300362s ]
[2025-07-16 19:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000226s ]
[2025-07-16 19:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000179s ]
[2025-07-16 19:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000203s ]
[2025-07-16 19:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001505s ]
[2025-07-16 19:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-16 19:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000136s ]
[2025-07-16 19:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-16 19:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-16 19:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-16 19:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000093s ]
[2025-07-16 19:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752664203  WHERE (  `id` = 749986 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000188s ]
[2025-07-16 19:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000455s ]
[2025-07-16 19:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000264s ]
[2025-07-16 19:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000165s ]
[2025-07-16 19:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000167s ]
[2025-07-16 19:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000154s ]
[2025-07-16 19:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299554s ]
[2025-07-16 19:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000354s ]
[2025-07-16 19:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000297s ]
[2025-07-16 19:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000117s ]
[2025-07-16 19:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001419s ]
[2025-07-16 19:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000303s ]
[2025-07-16 19:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000303s ]
[2025-07-16 19:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000156s ]
[2025-07-16 19:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-16 19:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-16 19:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000097s ]
[2025-07-16 19:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752664203  WHERE (  `id` = 749987 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000126s ]
[2025-07-16 19:30:01][sql] CONNECT:[ UseTime:0.000492s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-16 19:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-16 18' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.005222s ]
[2025-07-16 19:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-16 18' [ RunTime:0.010916s ]
[2025-07-16 19:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004233s ]
[2025-07-16 19:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-16 18' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.001693s ]
[2025-07-16 19:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-16 18' [ RunTime:0.005599s ]
[2025-07-16 20:10:01][sql] CONNECT:[ UseTime:0.000364s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-16 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000293s ]
[2025-07-16 20:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000300s ]
[2025-07-16 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000239s ]
[2025-07-16 20:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000170s ]
[2025-07-16 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000269s ]
[2025-07-16 20:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002575s ]
[2025-07-16 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000258s ]
[2025-07-16 20:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000196s ]
[2025-07-16 20:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000220s ]
[2025-07-16 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000203s ]
[2025-07-16 20:10:01][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000117s ]
[2025-07-16 20:10:01][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000097s ]
[2025-07-16 20:10:01][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000094s ]
[2025-07-16 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000196s ]
[2025-07-16 20:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301556s ]
[2025-07-16 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000395s ]
[2025-07-16 20:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000265s ]
[2025-07-16 20:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000143s ]
[2025-07-16 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000226s ]
[2025-07-16 20:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000161s ]
[2025-07-16 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000227s ]
[2025-07-16 20:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001473s ]
[2025-07-16 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000216s ]
[2025-07-16 20:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000161s ]
[2025-07-16 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000220s ]
[2025-07-16 20:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000195s ]
[2025-07-16 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000212s ]
[2025-07-16 20:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000144s ]
[2025-07-16 20:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000142s ]
[2025-07-16 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000167s ]
[2025-07-16 20:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000096s ]
[2025-07-16 20:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000151s ]
[2025-07-16 20:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752667801  WHERE (  `id` = 749982 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000176s ]
[2025-07-16 20:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000159s ]
[2025-07-16 20:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000189s ]
[2025-07-16 20:10:01][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000171s ]
[2025-07-16 20:10:01][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000153s ]
[2025-07-16 20:10:01][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000154s ]
[2025-07-16 20:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299441s ]
[2025-07-16 20:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000333s ]
[2025-07-16 20:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000286s ]
[2025-07-16 20:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000202s ]
[2025-07-16 20:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001470s ]
[2025-07-16 20:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-16 20:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000135s ]
[2025-07-16 20:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000295s ]
[2025-07-16 20:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000248s ]
[2025-07-16 20:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000145s ]
[2025-07-16 20:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000210s ]
[2025-07-16 20:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752667801  WHERE (  `id` = 749983 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000127s ]
[2025-07-16 20:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000291s ]
[2025-07-16 20:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000307s ]
[2025-07-16 20:10:01][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000215s ]
[2025-07-16 20:10:01][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000109s ]
[2025-07-16 20:10:01][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000113s ]
[2025-07-16 20:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300006s ]
[2025-07-16 20:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000279s ]
[2025-07-16 20:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000228s ]
[2025-07-16 20:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000215s ]
[2025-07-16 20:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001452s ]
[2025-07-16 20:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000149s ]
[2025-07-16 20:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-16 20:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-16 20:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-16 20:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-16 20:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000112s ]
[2025-07-16 20:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752667802  WHERE (  `id` = 749984 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000116s ]
[2025-07-16 20:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000214s ]
[2025-07-16 20:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000133s ]
[2025-07-16 20:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300313s ]
[2025-07-16 20:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000160s ]
[2025-07-16 20:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000278s ]
[2025-07-16 20:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000198s ]
[2025-07-16 20:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001583s ]
[2025-07-16 20:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000178s ]
[2025-07-16 20:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000142s ]
[2025-07-16 20:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000092s ]
[2025-07-16 20:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000102s ]
[2025-07-16 20:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-16 20:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000100s ]
[2025-07-16 20:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752667802  WHERE (  `id` = 749985 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-16 20:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000572s ]
[2025-07-16 20:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000211s ]
[2025-07-16 20:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000166s ]
[2025-07-16 20:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000143s ]
[2025-07-16 20:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000143s ]
[2025-07-16 20:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.306563s ]
[2025-07-16 20:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000324s ]
[2025-07-16 20:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000207s ]
[2025-07-16 20:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000175s ]
[2025-07-16 20:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001488s ]
[2025-07-16 20:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-16 20:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000141s ]
[2025-07-16 20:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000092s ]
[2025-07-16 20:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-16 20:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-16 20:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000093s ]
[2025-07-16 20:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752667802  WHERE (  `id` = 749986 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000122s ]
[2025-07-16 20:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000351s ]
[2025-07-16 20:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000253s ]
[2025-07-16 20:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000163s ]
[2025-07-16 20:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000153s ]
[2025-07-16 20:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000152s ]
[2025-07-16 20:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299864s ]
[2025-07-16 20:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000197s ]
[2025-07-16 20:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000093s ]
[2025-07-16 20:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-16 20:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001432s ]
[2025-07-16 20:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-16 20:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000133s ]
[2025-07-16 20:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-16 20:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-16 20:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-16 20:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-16 20:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752667803  WHERE (  `id` = 749987 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-16 20:30:01][sql] CONNECT:[ UseTime:0.000425s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-16 20:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-16 19' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.005176s ]
[2025-07-16 20:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-16 19' [ RunTime:0.006772s ]
[2025-07-16 20:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.003505s ]
[2025-07-16 20:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-16 19' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000763s ]
[2025-07-16 20:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-16 19' [ RunTime:0.004513s ]
[2025-07-16 21:10:01][sql] CONNECT:[ UseTime:0.000384s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-16 21:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000310s ]
[2025-07-16 21:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000236s ]
[2025-07-16 21:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000271s ]
[2025-07-16 21:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000330s ]
[2025-07-16 21:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000413s ]
[2025-07-16 21:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002585s ]
[2025-07-16 21:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000232s ]
[2025-07-16 21:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000152s ]
[2025-07-16 21:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000143s ]
[2025-07-16 21:10:01][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000203s ]
[2025-07-16 21:10:01][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000118s ]
[2025-07-16 21:10:01][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000098s ]
[2025-07-16 21:10:01][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000094s ]
[2025-07-16 21:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000200s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.307865s ]
[2025-07-16 21:10:02][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000328s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000279s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000176s ]
[2025-07-16 21:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000243s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000234s ]
[2025-07-16 21:10:02][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000363s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001508s ]
[2025-07-16 21:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000218s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000166s ]
[2025-07-16 21:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000218s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000196s ]
[2025-07-16 21:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000169s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000097s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-16 21:10:02][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000253s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000161s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000176s ]
[2025-07-16 21:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752671402  WHERE (  `id` = 749982 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000185s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000156s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000189s ]
[2025-07-16 21:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000114s ]
[2025-07-16 21:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000117s ]
[2025-07-16 21:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000098s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.304283s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000358s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000337s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000347s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001521s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000158s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000147s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000158s ]
[2025-07-16 21:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752671402  WHERE (  `id` = 749983 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000178s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000178s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000254s ]
[2025-07-16 21:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000163s ]
[2025-07-16 21:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000154s ]
[2025-07-16 21:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000098s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300053s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000147s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000311s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000284s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001561s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000169s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000202s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000151s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000153s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000098s ]
[2025-07-16 21:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752671402  WHERE (  `id` = 749984 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000139s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000265s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000260s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300882s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000248s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000151s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001561s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000190s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000190s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000091s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-16 21:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-16 21:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752671402  WHERE (  `id` = 749985 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000347s ]
[2025-07-16 21:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000224s ]
[2025-07-16 21:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000122s ]
[2025-07-16 21:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000099s ]
[2025-07-16 21:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000096s ]
[2025-07-16 21:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300374s ]
[2025-07-16 21:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000218s ]
[2025-07-16 21:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000183s ]
[2025-07-16 21:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-16 21:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001425s ]
[2025-07-16 21:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000178s ]
[2025-07-16 21:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000243s ]
[2025-07-16 21:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000252s ]
[2025-07-16 21:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000152s ]
[2025-07-16 21:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000155s ]
[2025-07-16 21:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000159s ]
[2025-07-16 21:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752671403  WHERE (  `id` = 749986 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000178s ]
[2025-07-16 21:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000348s ]
[2025-07-16 21:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000139s ]
[2025-07-16 21:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000114s ]
[2025-07-16 21:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000095s ]
[2025-07-16 21:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000157s ]
[2025-07-16 21:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300875s ]
[2025-07-16 21:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000333s ]
[2025-07-16 21:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000295s ]
[2025-07-16 21:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000251s ]
[2025-07-16 21:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001576s ]
[2025-07-16 21:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000169s ]
[2025-07-16 21:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000195s ]
[2025-07-16 21:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000157s ]
[2025-07-16 21:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000149s ]
[2025-07-16 21:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000150s ]
[2025-07-16 21:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000099s ]
[2025-07-16 21:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752671403  WHERE (  `id` = 749987 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-16 21:30:01][sql] CONNECT:[ UseTime:0.000469s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-16 21:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-16 20' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.006689s ]
[2025-07-16 21:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-16 20' [ RunTime:0.008085s ]
[2025-07-16 21:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004137s ]
[2025-07-16 21:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-16 20' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000639s ]
[2025-07-16 21:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-16 20' [ RunTime:0.002185s ]
[2025-07-16 22:10:01][sql] CONNECT:[ UseTime:0.000417s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-16 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000519s ]
[2025-07-16 22:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000411s ]
[2025-07-16 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000415s ]
[2025-07-16 22:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000272s ]
[2025-07-16 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000287s ]
[2025-07-16 22:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002642s ]
[2025-07-16 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000227s ]
[2025-07-16 22:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000163s ]
[2025-07-16 22:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000243s ]
[2025-07-16 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000275s ]
[2025-07-16 22:10:01][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000188s ]
[2025-07-16 22:10:01][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000168s ]
[2025-07-16 22:10:01][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000173s ]
[2025-07-16 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000235s ]
[2025-07-16 22:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302079s ]
[2025-07-16 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000422s ]
[2025-07-16 22:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000255s ]
[2025-07-16 22:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000167s ]
[2025-07-16 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000169s ]
[2025-07-16 22:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000115s ]
[2025-07-16 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000179s ]
[2025-07-16 22:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001424s ]
[2025-07-16 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000196s ]
[2025-07-16 22:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-16 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000157s ]
[2025-07-16 22:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000141s ]
[2025-07-16 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000150s ]
[2025-07-16 22:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000093s ]
[2025-07-16 22:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-16 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000147s ]
[2025-07-16 22:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-16 22:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000092s ]
[2025-07-16 22:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752675001  WHERE (  `id` = 749982 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000118s ]
[2025-07-16 22:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000267s ]
[2025-07-16 22:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000254s ]
[2025-07-16 22:10:01][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000166s ]
[2025-07-16 22:10:01][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000154s ]
[2025-07-16 22:10:01][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000153s ]
[2025-07-16 22:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300142s ]
[2025-07-16 22:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000266s ]
[2025-07-16 22:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000163s ]
[2025-07-16 22:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-16 22:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001508s ]
[2025-07-16 22:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000210s ]
[2025-07-16 22:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000339s ]
[2025-07-16 22:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000166s ]
[2025-07-16 22:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000093s ]
[2025-07-16 22:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-16 22:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000111s ]
[2025-07-16 22:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752675001  WHERE (  `id` = 749983 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-16 22:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000260s ]
[2025-07-16 22:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000316s ]
[2025-07-16 22:10:01][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000171s ]
[2025-07-16 22:10:01][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000159s ]
[2025-07-16 22:10:01][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000155s ]
[2025-07-16 22:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300289s ]
[2025-07-16 22:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000218s ]
[2025-07-16 22:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000229s ]
[2025-07-16 22:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000117s ]
[2025-07-16 22:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001483s ]
[2025-07-16 22:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000195s ]
[2025-07-16 22:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000221s ]
[2025-07-16 22:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000171s ]
[2025-07-16 22:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000092s ]
[2025-07-16 22:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-16 22:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000112s ]
[2025-07-16 22:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752675002  WHERE (  `id` = 749984 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-16 22:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000244s ]
[2025-07-16 22:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000133s ]
[2025-07-16 22:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300548s ]
[2025-07-16 22:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000385s ]
[2025-07-16 22:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000266s ]
[2025-07-16 22:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000201s ]
[2025-07-16 22:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001515s ]
[2025-07-16 22:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-16 22:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000131s ]
[2025-07-16 22:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-16 22:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-16 22:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-16 22:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000101s ]
[2025-07-16 22:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752675002  WHERE (  `id` = 749985 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-16 22:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000477s ]
[2025-07-16 22:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000291s ]
[2025-07-16 22:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000172s ]
[2025-07-16 22:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000156s ]
[2025-07-16 22:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000158s ]
[2025-07-16 22:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300074s ]
[2025-07-16 22:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000146s ]
[2025-07-16 22:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000090s ]
[2025-07-16 22:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-16 22:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001539s ]
[2025-07-16 22:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000172s ]
[2025-07-16 22:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000203s ]
[2025-07-16 22:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000145s ]
[2025-07-16 22:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000143s ]
[2025-07-16 22:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000142s ]
[2025-07-16 22:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000221s ]
[2025-07-16 22:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752675002  WHERE (  `id` = 749986 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000120s ]
[2025-07-16 22:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000288s ]
[2025-07-16 22:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000204s ]
[2025-07-16 22:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000112s ]
[2025-07-16 22:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000095s ]
[2025-07-16 22:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000093s ]
[2025-07-16 22:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300393s ]
[2025-07-16 22:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000284s ]
[2025-07-16 22:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000163s ]
[2025-07-16 22:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000170s ]
[2025-07-16 22:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001446s ]
[2025-07-16 22:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000165s ]
[2025-07-16 22:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000199s ]
[2025-07-16 22:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000150s ]
[2025-07-16 22:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000149s ]
[2025-07-16 22:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000147s ]
[2025-07-16 22:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000163s ]
[2025-07-16 22:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752675003  WHERE (  `id` = 749987 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-16 22:30:01][sql] CONNECT:[ UseTime:0.000365s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-16 22:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-16 21' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.005279s ]
[2025-07-16 22:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-16 21' [ RunTime:0.008405s ]
[2025-07-16 22:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004724s ]
[2025-07-16 22:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-16 21' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000663s ]
[2025-07-16 22:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-16 21' [ RunTime:0.004855s ]
[2025-07-16 23:10:01][sql] CONNECT:[ UseTime:0.000445s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-16 23:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000430s ]
[2025-07-16 23:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000271s ]
[2025-07-16 23:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000412s ]
[2025-07-16 23:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000286s ]
[2025-07-16 23:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000380s ]
[2025-07-16 23:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002637s ]
[2025-07-16 23:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000406s ]
[2025-07-16 23:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000266s ]
[2025-07-16 23:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000235s ]
[2025-07-16 23:10:01][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000209s ]
[2025-07-16 23:10:01][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000120s ]
[2025-07-16 23:10:01][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000097s ]
[2025-07-16 23:10:01][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220228070007' [ RunTime:0.000092s ]
[2025-07-16 23:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000198s ]
[2025-07-16 23:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299907s ]
[2025-07-16 23:10:02][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000308s ]
[2025-07-16 23:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000112s ]
[2025-07-16 23:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000091s ]
[2025-07-16 23:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000165s ]
[2025-07-16 23:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000115s ]
[2025-07-16 23:10:02][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000166s ]
[2025-07-16 23:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001507s ]
[2025-07-16 23:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000220s ]
[2025-07-16 23:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000166s ]
[2025-07-16 23:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000224s ]
[2025-07-16 23:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000198s ]
[2025-07-16 23:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000219s ]
[2025-07-16 23:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000155s ]
[2025-07-16 23:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000140s ]
[2025-07-16 23:10:02][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000212s ]
[2025-07-16 23:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000150s ]
[2025-07-16 23:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000156s ]
[2025-07-16 23:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752678602  WHERE (  `id` = 749982 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000184s ]
[2025-07-16 23:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000191s ]
[2025-07-16 23:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000248s ]
[2025-07-16 23:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000159s ]
[2025-07-16 23:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000153s ]
[2025-07-16 23:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20220523070001' [ RunTime:0.000098s ]
[2025-07-16 23:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299024s ]
[2025-07-16 23:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000281s ]
[2025-07-16 23:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000101s ]
[2025-07-16 23:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-16 23:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001446s ]
[2025-07-16 23:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000155s ]
[2025-07-16 23:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-16 23:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-16 23:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-16 23:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-16 23:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000090s ]
[2025-07-16 23:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752678602  WHERE (  `id` = 749983 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-16 23:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000178s ]
[2025-07-16 23:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000148s ]
[2025-07-16 23:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000107s ]
[2025-07-16 23:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000192s ]
[2025-07-16 23:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20230213070003' [ RunTime:0.000109s ]
[2025-07-16 23:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299360s ]
[2025-07-16 23:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000202s ]
[2025-07-16 23:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000263s ]
[2025-07-16 23:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000209s ]
[2025-07-16 23:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001474s ]
[2025-07-16 23:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000106s ]
[2025-07-16 23:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000133s ]
[2025-07-16 23:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-16 23:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-16 23:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-16 23:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-16 23:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752678602  WHERE (  `id` = 749984 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000106s ]
[2025-07-16 23:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000248s ]
[2025-07-16 23:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000190s ]
[2025-07-16 23:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300509s ]
[2025-07-16 23:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000313s ]
[2025-07-16 23:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000297s ]
[2025-07-16 23:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000182s ]
[2025-07-16 23:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001514s ]
[2025-07-16 23:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-16 23:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000134s ]
[2025-07-16 23:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-16 23:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-16 23:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-16 23:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-16 23:10:03][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752678603  WHERE (  `id` = 749985 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-16 23:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000338s ]
[2025-07-16 23:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000195s ]
[2025-07-16 23:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000118s ]
[2025-07-16 23:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000100s ]
[2025-07-16 23:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000102s ]
[2025-07-16 23:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299356s ]
[2025-07-16 23:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000139s ]
[2025-07-16 23:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000089s ]
[2025-07-16 23:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000296s ]
[2025-07-16 23:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001522s ]
[2025-07-16 23:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000115s ]
[2025-07-16 23:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000136s ]
[2025-07-16 23:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-16 23:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-16 23:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-16 23:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000099s ]
[2025-07-16 23:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752678603  WHERE (  `id` = 749986 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-16 23:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000287s ]
[2025-07-16 23:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000130s ]
[2025-07-16 23:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000109s ]
[2025-07-16 23:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000096s ]
[2025-07-16 23:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-16 00:00:00' AND '2025-07-16 23:59:59'  AND `sn` = '20240930070003' [ RunTime:0.000094s ]
[2025-07-16 23:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1752595200 AND 1752681599 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299553s ]
[2025-07-16 23:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000202s ]
[2025-07-16 23:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000175s ]
[2025-07-16 23:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000197s ]
[2025-07-16 23:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001443s ]
[2025-07-16 23:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000102s ]
[2025-07-16 23:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1752681599 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000132s ]
[2025-07-16 23:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-16 23:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000144s ]
[2025-07-16 23:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1752681599 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000147s ]
[2025-07-16 23:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000162s ]
[2025-07-16 23:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1752678603  WHERE (  `id` = 749987 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000173s ]
[2025-07-16 23:30:01][sql] CONNECT:[ UseTime:0.000451s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-16 23:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-16 22' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.005088s ]
[2025-07-16 23:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-16 22' [ RunTime:0.005603s ]
[2025-07-16 23:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004771s ]
[2025-07-16 23:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-16 22' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000902s ]
[2025-07-16 23:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-16 22' [ RunTime:0.004609s ]
