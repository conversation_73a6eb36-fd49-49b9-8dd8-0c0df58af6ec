[2025-07-22 00:10:01][sql] CONNECT:[ UseTime:0.000378s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 00:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000314s ]
[2025-07-22 00:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000461s ]
[2025-07-22 00:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000411s ]
[2025-07-22 00:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000337s ]
[2025-07-22 00:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000273s ]
[2025-07-22 00:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002560s ]
[2025-07-22 00:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000263s ]
[2025-07-22 00:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000117s ]
[2025-07-22 00:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000129s ]
[2025-07-22 00:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000202s ]
[2025-07-22 00:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300975s ]
[2025-07-22 00:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000178s ]
[2025-07-22 00:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000255s ]
[2025-07-22 00:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000237s ]
[2025-07-22 00:10:02][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000248s ]
[2025-07-22 00:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001514s ]
[2025-07-22 00:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000229s ]
[2025-07-22 00:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000175s ]
[2025-07-22 00:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000225s ]
[2025-07-22 00:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000205s ]
[2025-07-22 00:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000168s ]
[2025-07-22 00:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000096s ]
[2025-07-22 00:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-22 00:10:02][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000341s ]
[2025-07-22 00:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000106s ]
[2025-07-22 00:10:02][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000239s ]
[2025-07-22 00:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000158s ]
[2025-07-22 00:10:02][sql] INSERT INTO `ea_breed_data` SET `farmid` = '0216' , `batch_id` = 47 , `prod_id` = 897 , `prod_date` = 1753113600 , `cunlan` = 0 , `days` = 20291 [ RunTime:0.004942s ]
[2025-07-22 00:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000174s ]
[2025-07-22 00:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000246s ]
[2025-07-22 00:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299901s ]
[2025-07-22 00:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000269s ]
[2025-07-22 00:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000119s ]
[2025-07-22 00:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001487s ]
[2025-07-22 00:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000299s ]
[2025-07-22 00:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000194s ]
[2025-07-22 00:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000096s ]
[2025-07-22 00:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-22 00:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 00:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000093s ]
[2025-07-22 00:10:02][sql] INSERT INTO `ea_breed_data` SET `farmid` = '0216' , `batch_id` = 24 , `prod_id` = 872 , `prod_date` = 1753113600 , `cunlan` = 0 , `days` = 20291 [ RunTime:0.004892s ]
[2025-07-22 00:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000153s ]
[2025-07-22 00:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000131s ]
[2025-07-22 00:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.298999s ]
[2025-07-22 00:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000190s ]
[2025-07-22 00:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000122s ]
[2025-07-22 00:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001476s ]
[2025-07-22 00:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000194s ]
[2025-07-22 00:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000200s ]
[2025-07-22 00:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000140s ]
[2025-07-22 00:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-22 00:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 00:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000101s ]
[2025-07-22 00:10:02][sql] INSERT INTO `ea_breed_data` SET `farmid` = '0216' , `batch_id` = 157 , `prod_id` = 1030 , `prod_date` = 1753113600 , `cunlan` = 37331 , `days` = 449 [ RunTime:0.005110s ]
[2025-07-22 00:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000212s ]
[2025-07-22 00:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000188s ]
[2025-07-22 00:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299721s ]
[2025-07-22 00:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000369s ]
[2025-07-22 00:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000350s ]
[2025-07-22 00:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001710s ]
[2025-07-22 00:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000176s ]
[2025-07-22 00:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000141s ]
[2025-07-22 00:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000093s ]
[2025-07-22 00:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-22 00:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-22 00:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000230s ]
[2025-07-22 00:10:03][sql] INSERT INTO `ea_breed_data` SET `farmid` = '0216' , `batch_id` = 460 , `prod_id` = 1332 , `prod_date` = 1753113600 , `cunlan` = 17996 , `days` = 370 [ RunTime:0.004942s ]
[2025-07-22 00:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000465s ]
[2025-07-22 00:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000260s ]
[2025-07-22 00:10:03][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000273s ]
[2025-07-22 00:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000166s ]
[2025-07-22 00:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000168s ]
[2025-07-22 00:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000164s ]
[2025-07-22 00:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299960s ]
[2025-07-22 00:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000131s ]
[2025-07-22 00:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000115s ]
[2025-07-22 00:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001506s ]
[2025-07-22 00:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000154s ]
[2025-07-22 00:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-22 00:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-22 00:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 00:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-22 00:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000116s ]
[2025-07-22 00:10:03][sql] INSERT INTO `ea_breed_data` SET `farmid` = '0216' , `batch_id` = 844 , `prod_id` = 1724 , `prod_date` = 1753113600 , `cunlan` = 0 , `days` = 20292 [ RunTime:0.003993s ]
[2025-07-22 00:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000335s ]
[2025-07-22 00:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000128s ]
[2025-07-22 00:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299022s ]
[2025-07-22 00:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000273s ]
[2025-07-22 00:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000174s ]
[2025-07-22 00:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001458s ]
[2025-07-22 00:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000194s ]
[2025-07-22 00:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000182s ]
[2025-07-22 00:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000091s ]
[2025-07-22 00:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000103s ]
[2025-07-22 00:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-22 00:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000099s ]
[2025-07-22 00:10:03][sql] INSERT INTO `ea_breed_data` SET `farmid` = '0216' , `batch_id` = 843 , `prod_id` = 1723 , `prod_date` = 1753113600 , `cunlan` = 0 , `days` = 20292 [ RunTime:0.004865s ]
[2025-07-22 00:30:01][sql] CONNECT:[ UseTime:0.000295s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 00:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-21 23' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.009567s ]
[2025-07-22 00:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-21 23' [ RunTime:0.008578s ]
[2025-07-22 00:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004449s ]
[2025-07-22 00:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-21 23' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.001566s ]
[2025-07-22 00:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-21 23' [ RunTime:0.005395s ]
[2025-07-22 01:10:01][sql] CONNECT:[ UseTime:0.000451s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000449s ]
[2025-07-22 01:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000179s ]
[2025-07-22 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000192s ]
[2025-07-22 01:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000124s ]
[2025-07-22 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000272s ]
[2025-07-22 01:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002515s ]
[2025-07-22 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000224s ]
[2025-07-22 01:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000168s ]
[2025-07-22 01:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000260s ]
[2025-07-22 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000262s ]
[2025-07-22 01:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299606s ]
[2025-07-22 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000253s ]
[2025-07-22 01:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000178s ]
[2025-07-22 01:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000298s ]
[2025-07-22 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000338s ]
[2025-07-22 01:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000188s ]
[2025-07-22 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000235s ]
[2025-07-22 01:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001489s ]
[2025-07-22 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000220s ]
[2025-07-22 01:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000117s ]
[2025-07-22 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000156s ]
[2025-07-22 01:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000141s ]
[2025-07-22 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000220s ]
[2025-07-22 01:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000155s ]
[2025-07-22 01:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000144s ]
[2025-07-22 01:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000207s ]
[2025-07-22 01:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000097s ]
[2025-07-22 01:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-22 01:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753117801  WHERE (  `id` = 750018 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000124s ]
[2025-07-22 01:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000153s ]
[2025-07-22 01:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000177s ]
[2025-07-22 01:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300242s ]
[2025-07-22 01:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000288s ]
[2025-07-22 01:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000266s ]
[2025-07-22 01:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000245s ]
[2025-07-22 01:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001530s ]
[2025-07-22 01:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000157s ]
[2025-07-22 01:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000193s ]
[2025-07-22 01:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000141s ]
[2025-07-22 01:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-22 01:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-22 01:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000091s ]
[2025-07-22 01:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753117801  WHERE (  `id` = 750019 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000117s ]
[2025-07-22 01:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000177s ]
[2025-07-22 01:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000183s ]
[2025-07-22 01:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299390s ]
[2025-07-22 01:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000291s ]
[2025-07-22 01:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000269s ]
[2025-07-22 01:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000174s ]
[2025-07-22 01:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001543s ]
[2025-07-22 01:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-22 01:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000136s ]
[2025-07-22 01:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000158s ]
[2025-07-22 01:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000145s ]
[2025-07-22 01:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000146s ]
[2025-07-22 01:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000158s ]
[2025-07-22 01:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753117802  WHERE (  `id` = 750020 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000172s ]
[2025-07-22 01:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000241s ]
[2025-07-22 01:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000179s ]
[2025-07-22 01:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299895s ]
[2025-07-22 01:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000268s ]
[2025-07-22 01:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000100s ]
[2025-07-22 01:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-22 01:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001520s ]
[2025-07-22 01:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000115s ]
[2025-07-22 01:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000136s ]
[2025-07-22 01:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-22 01:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000101s ]
[2025-07-22 01:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-22 01:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-22 01:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753117802  WHERE (  `id` = 750021 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000125s ]
[2025-07-22 01:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000330s ]
[2025-07-22 01:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000244s ]
[2025-07-22 01:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000447s ]
[2025-07-22 01:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000171s ]
[2025-07-22 01:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000163s ]
[2025-07-22 01:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000164s ]
[2025-07-22 01:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300410s ]
[2025-07-22 01:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000259s ]
[2025-07-22 01:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000181s ]
[2025-07-22 01:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000163s ]
[2025-07-22 01:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001487s ]
[2025-07-22 01:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000160s ]
[2025-07-22 01:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000194s ]
[2025-07-22 01:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000146s ]
[2025-07-22 01:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000141s ]
[2025-07-22 01:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000146s ]
[2025-07-22 01:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000148s ]
[2025-07-22 01:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753117802  WHERE (  `id` = 750022 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000174s ]
[2025-07-22 01:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000446s ]
[2025-07-22 01:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000253s ]
[2025-07-22 01:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.303620s ]
[2025-07-22 01:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000314s ]
[2025-07-22 01:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000318s ]
[2025-07-22 01:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000335s ]
[2025-07-22 01:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001566s ]
[2025-07-22 01:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000266s ]
[2025-07-22 01:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000252s ]
[2025-07-22 01:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000152s ]
[2025-07-22 01:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000140s ]
[2025-07-22 01:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000099s ]
[2025-07-22 01:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000102s ]
[2025-07-22 01:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753117803  WHERE (  `id` = 750023 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000132s ]
[2025-07-22 01:30:01][sql] CONNECT:[ UseTime:0.000419s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 01:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 00' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.007339s ]
[2025-07-22 01:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 00' [ RunTime:0.004726s ]
[2025-07-22 01:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.003433s ]
[2025-07-22 01:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 00' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000393s ]
[2025-07-22 01:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 00' [ RunTime:0.004122s ]
[2025-07-22 02:10:01][sql] CONNECT:[ UseTime:0.000460s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 02:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000379s ]
[2025-07-22 02:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000227s ]
[2025-07-22 02:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000239s ]
[2025-07-22 02:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000139s ]
[2025-07-22 02:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000515s ]
[2025-07-22 02:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.003750s ]
[2025-07-22 02:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000537s ]
[2025-07-22 02:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000252s ]
[2025-07-22 02:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000254s ]
[2025-07-22 02:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000406s ]
[2025-07-22 02:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.307902s ]
[2025-07-22 02:10:02][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000334s ]
[2025-07-22 02:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000136s ]
[2025-07-22 02:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000301s ]
[2025-07-22 02:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000377s ]
[2025-07-22 02:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000131s ]
[2025-07-22 02:10:02][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000175s ]
[2025-07-22 02:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001445s ]
[2025-07-22 02:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000156s ]
[2025-07-22 02:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-22 02:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000153s ]
[2025-07-22 02:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000140s ]
[2025-07-22 02:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000147s ]
[2025-07-22 02:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000093s ]
[2025-07-22 02:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-22 02:10:02][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000148s ]
[2025-07-22 02:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-22 02:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000093s ]
[2025-07-22 02:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753121402  WHERE (  `id` = 750018 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000125s ]
[2025-07-22 02:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000116s ]
[2025-07-22 02:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000132s ]
[2025-07-22 02:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299280s ]
[2025-07-22 02:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000257s ]
[2025-07-22 02:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000293s ]
[2025-07-22 02:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000194s ]
[2025-07-22 02:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001504s ]
[2025-07-22 02:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000167s ]
[2025-07-22 02:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000206s ]
[2025-07-22 02:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000158s ]
[2025-07-22 02:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000155s ]
[2025-07-22 02:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000145s ]
[2025-07-22 02:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000093s ]
[2025-07-22 02:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753121402  WHERE (  `id` = 750019 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000115s ]
[2025-07-22 02:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000180s ]
[2025-07-22 02:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000129s ]
[2025-07-22 02:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299123s ]
[2025-07-22 02:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000174s ]
[2025-07-22 02:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000142s ]
[2025-07-22 02:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-22 02:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001499s ]
[2025-07-22 02:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000106s ]
[2025-07-22 02:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000132s ]
[2025-07-22 02:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-22 02:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-22 02:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 02:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000092s ]
[2025-07-22 02:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753121402  WHERE (  `id` = 750020 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-22 02:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000211s ]
[2025-07-22 02:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000123s ]
[2025-07-22 02:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299717s ]
[2025-07-22 02:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000128s ]
[2025-07-22 02:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000295s ]
[2025-07-22 02:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000311s ]
[2025-07-22 02:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001551s ]
[2025-07-22 02:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000174s ]
[2025-07-22 02:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000198s ]
[2025-07-22 02:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000153s ]
[2025-07-22 02:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-22 02:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-22 02:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000097s ]
[2025-07-22 02:10:03][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753121403  WHERE (  `id` = 750021 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000115s ]
[2025-07-22 02:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000375s ]
[2025-07-22 02:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000180s ]
[2025-07-22 02:10:03][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000242s ]
[2025-07-22 02:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000122s ]
[2025-07-22 02:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000102s ]
[2025-07-22 02:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000099s ]
[2025-07-22 02:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299858s ]
[2025-07-22 02:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000261s ]
[2025-07-22 02:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000158s ]
[2025-07-22 02:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000118s ]
[2025-07-22 02:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001529s ]
[2025-07-22 02:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000159s ]
[2025-07-22 02:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000138s ]
[2025-07-22 02:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000091s ]
[2025-07-22 02:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 02:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-22 02:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000093s ]
[2025-07-22 02:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753121403  WHERE (  `id` = 750022 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000119s ]
[2025-07-22 02:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000337s ]
[2025-07-22 02:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000246s ]
[2025-07-22 02:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300395s ]
[2025-07-22 02:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000213s ]
[2025-07-22 02:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000156s ]
[2025-07-22 02:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000175s ]
[2025-07-22 02:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001530s ]
[2025-07-22 02:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000165s ]
[2025-07-22 02:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000200s ]
[2025-07-22 02:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000149s ]
[2025-07-22 02:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000149s ]
[2025-07-22 02:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-22 02:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000097s ]
[2025-07-22 02:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753121403  WHERE (  `id` = 750023 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-22 02:30:01][sql] CONNECT:[ UseTime:0.000428s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 02:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 01' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.009582s ]
[2025-07-22 02:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 01' [ RunTime:0.005504s ]
[2025-07-22 02:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.003217s ]
[2025-07-22 02:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 01' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000556s ]
[2025-07-22 02:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 01' [ RunTime:0.002014s ]
[2025-07-22 03:10:01][sql] CONNECT:[ UseTime:0.000332s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000345s ]
[2025-07-22 03:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000177s ]
[2025-07-22 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000190s ]
[2025-07-22 03:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000307s ]
[2025-07-22 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000393s ]
[2025-07-22 03:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002489s ]
[2025-07-22 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000228s ]
[2025-07-22 03:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000112s ]
[2025-07-22 03:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000128s ]
[2025-07-22 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000199s ]
[2025-07-22 03:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301002s ]
[2025-07-22 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000402s ]
[2025-07-22 03:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000253s ]
[2025-07-22 03:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000163s ]
[2025-07-22 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000231s ]
[2025-07-22 03:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000164s ]
[2025-07-22 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000239s ]
[2025-07-22 03:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001478s ]
[2025-07-22 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000213s ]
[2025-07-22 03:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000162s ]
[2025-07-22 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000219s ]
[2025-07-22 03:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000199s ]
[2025-07-22 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000215s ]
[2025-07-22 03:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000144s ]
[2025-07-22 03:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-22 03:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000166s ]
[2025-07-22 03:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-22 03:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000091s ]
[2025-07-22 03:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753125001  WHERE (  `id` = 750018 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000349s ]
[2025-07-22 03:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000283s ]
[2025-07-22 03:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000252s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302256s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000320s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000167s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000181s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001527s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000178s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000199s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000167s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000256s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000153s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000158s ]
[2025-07-22 03:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753125002  WHERE (  `id` = 750019 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000172s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000221s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000181s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.305481s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000307s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000300s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000293s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001553s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000134s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000094s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000094s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000143s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000184s ]
[2025-07-22 03:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753125002  WHERE (  `id` = 750020 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000173s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000248s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000253s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.306523s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000366s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000233s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000207s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001656s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000305s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000204s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000097s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000099s ]
[2025-07-22 03:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753125002  WHERE (  `id` = 750021 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000121s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000329s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000132s ]
[2025-07-22 03:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000233s ]
[2025-07-22 03:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000130s ]
[2025-07-22 03:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000154s ]
[2025-07-22 03:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000106s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302920s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000131s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000332s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001542s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000165s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000198s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000151s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000091s ]
[2025-07-22 03:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-22 03:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753125002  WHERE (  `id` = 750022 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000394s ]
[2025-07-22 03:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000133s ]
[2025-07-22 03:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301552s ]
[2025-07-22 03:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000277s ]
[2025-07-22 03:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000164s ]
[2025-07-22 03:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-22 03:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001460s ]
[2025-07-22 03:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000261s ]
[2025-07-22 03:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000222s ]
[2025-07-22 03:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000099s ]
[2025-07-22 03:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-22 03:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 03:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000098s ]
[2025-07-22 03:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753125003  WHERE (  `id` = 750023 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-22 03:30:01][sql] CONNECT:[ UseTime:0.000407s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 03:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 02' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.007270s ]
[2025-07-22 03:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 02' [ RunTime:0.007534s ]
[2025-07-22 03:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004131s ]
[2025-07-22 03:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 02' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000670s ]
[2025-07-22 03:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 02' [ RunTime:0.004102s ]
[2025-07-22 04:10:01][sql] CONNECT:[ UseTime:0.000416s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 04:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000378s ]
[2025-07-22 04:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000176s ]
[2025-07-22 04:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000189s ]
[2025-07-22 04:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000327s ]
[2025-07-22 04:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000375s ]
[2025-07-22 04:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002580s ]
[2025-07-22 04:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000273s ]
[2025-07-22 04:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000212s ]
[2025-07-22 04:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000257s ]
[2025-07-22 04:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000258s ]
[2025-07-22 04:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301745s ]
[2025-07-22 04:10:02][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000277s ]
[2025-07-22 04:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000279s ]
[2025-07-22 04:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000310s ]
[2025-07-22 04:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000332s ]
[2025-07-22 04:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000177s ]
[2025-07-22 04:10:02][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000237s ]
[2025-07-22 04:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001494s ]
[2025-07-22 04:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000226s ]
[2025-07-22 04:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000116s ]
[2025-07-22 04:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000159s ]
[2025-07-22 04:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000338s ]
[2025-07-22 04:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000225s ]
[2025-07-22 04:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000159s ]
[2025-07-22 04:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000154s ]
[2025-07-22 04:10:02][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000217s ]
[2025-07-22 04:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000149s ]
[2025-07-22 04:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000151s ]
[2025-07-22 04:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753128602  WHERE (  `id` = 750018 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000129s ]
[2025-07-22 04:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000122s ]
[2025-07-22 04:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000128s ]
[2025-07-22 04:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302503s ]
[2025-07-22 04:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000235s ]
[2025-07-22 04:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000159s ]
[2025-07-22 04:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000175s ]
[2025-07-22 04:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001450s ]
[2025-07-22 04:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000188s ]
[2025-07-22 04:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000143s ]
[2025-07-22 04:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000093s ]
[2025-07-22 04:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-22 04:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-22 04:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000092s ]
[2025-07-22 04:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753128602  WHERE (  `id` = 750019 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-22 04:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000191s ]
[2025-07-22 04:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000127s ]
[2025-07-22 04:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.304564s ]
[2025-07-22 04:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000284s ]
[2025-07-22 04:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000291s ]
[2025-07-22 04:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000272s ]
[2025-07-22 04:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001499s ]
[2025-07-22 04:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-22 04:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000133s ]
[2025-07-22 04:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-22 04:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-22 04:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000160s ]
[2025-07-22 04:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000168s ]
[2025-07-22 04:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753128602  WHERE (  `id` = 750020 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000173s ]
[2025-07-22 04:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000277s ]
[2025-07-22 04:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000218s ]
[2025-07-22 04:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.305768s ]
[2025-07-22 04:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000360s ]
[2025-07-22 04:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000309s ]
[2025-07-22 04:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000188s ]
[2025-07-22 04:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001514s ]
[2025-07-22 04:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000163s ]
[2025-07-22 04:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000140s ]
[2025-07-22 04:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000091s ]
[2025-07-22 04:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-22 04:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000147s ]
[2025-07-22 04:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000166s ]
[2025-07-22 04:10:03][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753128603  WHERE (  `id` = 750021 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000171s ]
[2025-07-22 04:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000348s ]
[2025-07-22 04:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000182s ]
[2025-07-22 04:10:03][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000273s ]
[2025-07-22 04:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000172s ]
[2025-07-22 04:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000108s ]
[2025-07-22 04:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000102s ]
[2025-07-22 04:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.305122s ]
[2025-07-22 04:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000231s ]
[2025-07-22 04:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000186s ]
[2025-07-22 04:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000118s ]
[2025-07-22 04:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001428s ]
[2025-07-22 04:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-22 04:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000134s ]
[2025-07-22 04:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000095s ]
[2025-07-22 04:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-22 04:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-22 04:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-22 04:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753128603  WHERE (  `id` = 750022 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000115s ]
[2025-07-22 04:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000459s ]
[2025-07-22 04:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000247s ]
[2025-07-22 04:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.303108s ]
[2025-07-22 04:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000331s ]
[2025-07-22 04:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000305s ]
[2025-07-22 04:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000287s ]
[2025-07-22 04:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001504s ]
[2025-07-22 04:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-22 04:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000193s ]
[2025-07-22 04:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000153s ]
[2025-07-22 04:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000152s ]
[2025-07-22 04:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000149s ]
[2025-07-22 04:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000162s ]
[2025-07-22 04:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753128603  WHERE (  `id` = 750023 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000175s ]
[2025-07-22 04:30:01][sql] CONNECT:[ UseTime:0.000419s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 04:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 03' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.004641s ]
[2025-07-22 04:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 03' [ RunTime:0.007427s ]
[2025-07-22 04:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.003850s ]
[2025-07-22 04:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 03' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000474s ]
[2025-07-22 04:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 03' [ RunTime:0.004017s ]
[2025-07-22 05:10:01][sql] CONNECT:[ UseTime:0.000391s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000296s ]
[2025-07-22 05:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000173s ]
[2025-07-22 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000419s ]
[2025-07-22 05:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000336s ]
[2025-07-22 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000324s ]
[2025-07-22 05:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002528s ]
[2025-07-22 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000249s ]
[2025-07-22 05:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000116s ]
[2025-07-22 05:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000129s ]
[2025-07-22 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000204s ]
[2025-07-22 05:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.310151s ]
[2025-07-22 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000243s ]
[2025-07-22 05:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000274s ]
[2025-07-22 05:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000100s ]
[2025-07-22 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000175s ]
[2025-07-22 05:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000116s ]
[2025-07-22 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000166s ]
[2025-07-22 05:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001431s ]
[2025-07-22 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000154s ]
[2025-07-22 05:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-22 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000152s ]
[2025-07-22 05:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000138s ]
[2025-07-22 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000151s ]
[2025-07-22 05:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000092s ]
[2025-07-22 05:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-22 05:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000148s ]
[2025-07-22 05:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-22 05:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000092s ]
[2025-07-22 05:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753132201  WHERE (  `id` = 750018 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000118s ]
[2025-07-22 05:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000173s ]
[2025-07-22 05:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000185s ]
[2025-07-22 05:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.315360s ]
[2025-07-22 05:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000306s ]
[2025-07-22 05:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000195s ]
[2025-07-22 05:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000201s ]
[2025-07-22 05:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001578s ]
[2025-07-22 05:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-22 05:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000139s ]
[2025-07-22 05:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000162s ]
[2025-07-22 05:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000102s ]
[2025-07-22 05:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000140s ]
[2025-07-22 05:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-22 05:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753132202  WHERE (  `id` = 750019 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000125s ]
[2025-07-22 05:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000138s ]
[2025-07-22 05:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000239s ]
[2025-07-22 05:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.312609s ]
[2025-07-22 05:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000230s ]
[2025-07-22 05:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000170s ]
[2025-07-22 05:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000128s ]
[2025-07-22 05:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001565s ]
[2025-07-22 05:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-22 05:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-22 05:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000134s ]
[2025-07-22 05:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000094s ]
[2025-07-22 05:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-22 05:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000103s ]
[2025-07-22 05:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753132202  WHERE (  `id` = 750020 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000115s ]
[2025-07-22 05:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000212s ]
[2025-07-22 05:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000133s ]
[2025-07-22 05:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.313285s ]
[2025-07-22 05:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000325s ]
[2025-07-22 05:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000299s ]
[2025-07-22 05:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000304s ]
[2025-07-22 05:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001514s ]
[2025-07-22 05:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000166s ]
[2025-07-22 05:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-22 05:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000091s ]
[2025-07-22 05:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-22 05:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000143s ]
[2025-07-22 05:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000165s ]
[2025-07-22 05:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753132202  WHERE (  `id` = 750021 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000175s ]
[2025-07-22 05:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000330s ]
[2025-07-22 05:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000191s ]
[2025-07-22 05:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000282s ]
[2025-07-22 05:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000172s ]
[2025-07-22 05:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000107s ]
[2025-07-22 05:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000102s ]
[2025-07-22 05:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.310394s ]
[2025-07-22 05:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000211s ]
[2025-07-22 05:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000099s ]
[2025-07-22 05:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000125s ]
[2025-07-22 05:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001500s ]
[2025-07-22 05:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-22 05:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000132s ]
[2025-07-22 05:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-22 05:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 05:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000079s ]
[2025-07-22 05:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-22 05:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753132203  WHERE (  `id` = 750022 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-22 05:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000288s ]
[2025-07-22 05:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000131s ]
[2025-07-22 05:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.309516s ]
[2025-07-22 05:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000332s ]
[2025-07-22 05:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000307s ]
[2025-07-22 05:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000323s ]
[2025-07-22 05:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001569s ]
[2025-07-22 05:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-22 05:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000136s ]
[2025-07-22 05:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-22 05:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-22 05:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-22 05:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000099s ]
[2025-07-22 05:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753132203  WHERE (  `id` = 750023 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-22 05:30:01][sql] CONNECT:[ UseTime:0.000419s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 05:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 04' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.005106s ]
[2025-07-22 05:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 04' [ RunTime:0.006266s ]
[2025-07-22 05:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004057s ]
[2025-07-22 05:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 04' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000705s ]
[2025-07-22 05:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 04' [ RunTime:0.004337s ]
[2025-07-22 06:10:01][sql] CONNECT:[ UseTime:0.000440s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000517s ]
[2025-07-22 06:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000179s ]
[2025-07-22 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000253s ]
[2025-07-22 06:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000178s ]
[2025-07-22 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000291s ]
[2025-07-22 06:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002632s ]
[2025-07-22 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000177s ]
[2025-07-22 06:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000111s ]
[2025-07-22 06:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000131s ]
[2025-07-22 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000203s ]
[2025-07-22 06:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300623s ]
[2025-07-22 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000409s ]
[2025-07-22 06:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000267s ]
[2025-07-22 06:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000265s ]
[2025-07-22 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000243s ]
[2025-07-22 06:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000172s ]
[2025-07-22 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000233s ]
[2025-07-22 06:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001477s ]
[2025-07-22 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000375s ]
[2025-07-22 06:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000216s ]
[2025-07-22 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000176s ]
[2025-07-22 06:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000145s ]
[2025-07-22 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000157s ]
[2025-07-22 06:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000096s ]
[2025-07-22 06:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-22 06:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000149s ]
[2025-07-22 06:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-22 06:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000092s ]
[2025-07-22 06:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753135801  WHERE (  `id` = 750018 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000118s ]
[2025-07-22 06:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000107s ]
[2025-07-22 06:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000126s ]
[2025-07-22 06:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302596s ]
[2025-07-22 06:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000123s ]
[2025-07-22 06:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000093s ]
[2025-07-22 06:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-22 06:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001515s ]
[2025-07-22 06:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000209s ]
[2025-07-22 06:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000210s ]
[2025-07-22 06:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000095s ]
[2025-07-22 06:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-22 06:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-22 06:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000092s ]
[2025-07-22 06:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753135801  WHERE (  `id` = 750019 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000116s ]
[2025-07-22 06:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000186s ]
[2025-07-22 06:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000123s ]
[2025-07-22 06:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.305143s ]
[2025-07-22 06:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000219s ]
[2025-07-22 06:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000169s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000183s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001528s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000165s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000200s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000156s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000148s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000146s ]
[2025-07-22 06:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000159s ]
[2025-07-22 06:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753135802  WHERE (  `id` = 750020 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000123s ]
[2025-07-22 06:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000306s ]
[2025-07-22 06:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000284s ]
[2025-07-22 06:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.303393s ]
[2025-07-22 06:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000329s ]
[2025-07-22 06:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000308s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000331s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001539s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000170s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000136s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000142s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000231s ]
[2025-07-22 06:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000276s ]
[2025-07-22 06:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753135802  WHERE (  `id` = 750021 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000245s ]
[2025-07-22 06:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000451s ]
[2025-07-22 06:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000240s ]
[2025-07-22 06:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000270s ]
[2025-07-22 06:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000178s ]
[2025-07-22 06:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000165s ]
[2025-07-22 06:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000164s ]
[2025-07-22 06:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302288s ]
[2025-07-22 06:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000314s ]
[2025-07-22 06:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000186s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000183s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001488s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000135s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-22 06:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-22 06:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753135802  WHERE (  `id` = 750022 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000126s ]
[2025-07-22 06:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000338s ]
[2025-07-22 06:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000258s ]
[2025-07-22 06:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301604s ]
[2025-07-22 06:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000215s ]
[2025-07-22 06:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000143s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001433s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000101s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000134s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 06:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-22 06:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000091s ]
[2025-07-22 06:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753135802  WHERE (  `id` = 750023 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-22 06:30:01][sql] CONNECT:[ UseTime:0.000455s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 06:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 05' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.005203s ]
[2025-07-22 06:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 05' [ RunTime:0.006799s ]
[2025-07-22 06:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004330s ]
[2025-07-22 06:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 05' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000564s ]
[2025-07-22 06:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 05' [ RunTime:0.004158s ]
[2025-07-22 07:10:01][sql] CONNECT:[ UseTime:0.000405s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 07:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000306s ]
[2025-07-22 07:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000173s ]
[2025-07-22 07:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000365s ]
[2025-07-22 07:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000298s ]
[2025-07-22 07:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000393s ]
[2025-07-22 07:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002653s ]
[2025-07-22 07:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000324s ]
[2025-07-22 07:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000165s ]
[2025-07-22 07:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000189s ]
[2025-07-22 07:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000257s ]
[2025-07-22 07:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.303352s ]
[2025-07-22 07:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000514s ]
[2025-07-22 07:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000413s ]
[2025-07-22 07:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000249s ]
[2025-07-22 07:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000239s ]
[2025-07-22 07:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000121s ]
[2025-07-22 07:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000174s ]
[2025-07-22 07:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001630s ]
[2025-07-22 07:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000229s ]
[2025-07-22 07:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000173s ]
[2025-07-22 07:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000228s ]
[2025-07-22 07:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000206s ]
[2025-07-22 07:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000221s ]
[2025-07-22 07:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000101s ]
[2025-07-22 07:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-22 07:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000151s ]
[2025-07-22 07:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-22 07:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000157s ]
[2025-07-22 07:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753139401  WHERE (  `id` = 750018 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000180s ]
[2025-07-22 07:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000160s ]
[2025-07-22 07:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000186s ]
[2025-07-22 07:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300674s ]
[2025-07-22 07:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000143s ]
[2025-07-22 07:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-22 07:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000117s ]
[2025-07-22 07:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001527s ]
[2025-07-22 07:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000174s ]
[2025-07-22 07:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000138s ]
[2025-07-22 07:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000092s ]
[2025-07-22 07:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-22 07:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-22 07:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000088s ]
[2025-07-22 07:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753139402  WHERE (  `id` = 750019 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-22 07:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000180s ]
[2025-07-22 07:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000254s ]
[2025-07-22 07:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302878s ]
[2025-07-22 07:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000305s ]
[2025-07-22 07:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000296s ]
[2025-07-22 07:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000210s ]
[2025-07-22 07:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001452s ]
[2025-07-22 07:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-22 07:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000134s ]
[2025-07-22 07:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-22 07:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-22 07:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-22 07:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000097s ]
[2025-07-22 07:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753139402  WHERE (  `id` = 750020 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000116s ]
[2025-07-22 07:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000263s ]
[2025-07-22 07:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000245s ]
[2025-07-22 07:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.303975s ]
[2025-07-22 07:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000345s ]
[2025-07-22 07:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000248s ]
[2025-07-22 07:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000219s ]
[2025-07-22 07:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001536s ]
[2025-07-22 07:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000302s ]
[2025-07-22 07:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000152s ]
[2025-07-22 07:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000094s ]
[2025-07-22 07:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-22 07:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-22 07:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-22 07:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753139402  WHERE (  `id` = 750021 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-22 07:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000328s ]
[2025-07-22 07:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000298s ]
[2025-07-22 07:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000287s ]
[2025-07-22 07:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000182s ]
[2025-07-22 07:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000167s ]
[2025-07-22 07:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000163s ]
[2025-07-22 07:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301033s ]
[2025-07-22 07:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000304s ]
[2025-07-22 07:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000155s ]
[2025-07-22 07:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000116s ]
[2025-07-22 07:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001494s ]
[2025-07-22 07:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000157s ]
[2025-07-22 07:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-22 07:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-22 07:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-22 07:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 07:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-22 07:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753139403  WHERE (  `id` = 750022 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-22 07:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000332s ]
[2025-07-22 07:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000128s ]
[2025-07-22 07:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299881s ]
[2025-07-22 07:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000132s ]
[2025-07-22 07:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000290s ]
[2025-07-22 07:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000309s ]
[2025-07-22 07:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001541s ]
[2025-07-22 07:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000166s ]
[2025-07-22 07:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000198s ]
[2025-07-22 07:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000150s ]
[2025-07-22 07:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000154s ]
[2025-07-22 07:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-22 07:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-22 07:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753139403  WHERE (  `id` = 750023 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000130s ]
[2025-07-22 07:30:01][sql] CONNECT:[ UseTime:0.000426s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 07:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 06' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.007476s ]
[2025-07-22 07:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 06' [ RunTime:0.005917s ]
[2025-07-22 07:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.003352s ]
[2025-07-22 07:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 06' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000494s ]
[2025-07-22 07:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 06' [ RunTime:0.002268s ]
[2025-07-22 08:10:01][sql] CONNECT:[ UseTime:0.000280s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000298s ]
[2025-07-22 08:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000258s ]
[2025-07-22 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000364s ]
[2025-07-22 08:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000174s ]
[2025-07-22 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000276s ]
[2025-07-22 08:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002565s ]
[2025-07-22 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000172s ]
[2025-07-22 08:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000109s ]
[2025-07-22 08:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000126s ]
[2025-07-22 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000196s ]
[2025-07-22 08:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301106s ]
[2025-07-22 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000257s ]
[2025-07-22 08:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000167s ]
[2025-07-22 08:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000159s ]
[2025-07-22 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000223s ]
[2025-07-22 08:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000287s ]
[2025-07-22 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000278s ]
[2025-07-22 08:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001493s ]
[2025-07-22 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000213s ]
[2025-07-22 08:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000164s ]
[2025-07-22 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000218s ]
[2025-07-22 08:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000191s ]
[2025-07-22 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000170s ]
[2025-07-22 08:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000096s ]
[2025-07-22 08:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-22 08:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000148s ]
[2025-07-22 08:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-22 08:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000091s ]
[2025-07-22 08:10:01][sql] UPDATE `ea_breed_data`  SET `days` = 20292 , `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753143001  WHERE (  `id` = 750018 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000125s ]
[2025-07-22 08:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000119s ]
[2025-07-22 08:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000129s ]
[2025-07-22 08:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299451s ]
[2025-07-22 08:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000200s ]
[2025-07-22 08:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-22 08:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000142s ]
[2025-07-22 08:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001415s ]
[2025-07-22 08:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000105s ]
[2025-07-22 08:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000132s ]
[2025-07-22 08:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-22 08:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-22 08:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000079s ]
[2025-07-22 08:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000088s ]
[2025-07-22 08:10:01][sql] UPDATE `ea_breed_data`  SET `days` = 20292 , `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753143001  WHERE (  `id` = 750019 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000105s ]
[2025-07-22 08:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000178s ]
[2025-07-22 08:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000180s ]
[2025-07-22 08:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299319s ]
[2025-07-22 08:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000244s ]
[2025-07-22 08:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000153s ]
[2025-07-22 08:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000180s ]
[2025-07-22 08:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001469s ]
[2025-07-22 08:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000157s ]
[2025-07-22 08:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000136s ]
[2025-07-22 08:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-22 08:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-22 08:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000079s ]
[2025-07-22 08:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-22 08:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753143002  WHERE (  `id` = 750020 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-22 08:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000206s ]
[2025-07-22 08:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000204s ]
[2025-07-22 08:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299560s ]
[2025-07-22 08:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000185s ]
[2025-07-22 08:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000299s ]
[2025-07-22 08:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000281s ]
[2025-07-22 08:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001544s ]
[2025-07-22 08:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000128s ]
[2025-07-22 08:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000148s ]
[2025-07-22 08:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-22 08:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-22 08:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000079s ]
[2025-07-22 08:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000110s ]
[2025-07-22 08:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753143002  WHERE (  `id` = 750021 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-22 08:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000338s ]
[2025-07-22 08:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000180s ]
[2025-07-22 08:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000271s ]
[2025-07-22 08:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000186s ]
[2025-07-22 08:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000166s ]
[2025-07-22 08:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000164s ]
[2025-07-22 08:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299539s ]
[2025-07-22 08:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000272s ]
[2025-07-22 08:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000154s ]
[2025-07-22 08:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-22 08:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001456s ]
[2025-07-22 08:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000159s ]
[2025-07-22 08:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000153s ]
[2025-07-22 08:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-22 08:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-22 08:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 08:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-22 08:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753143002  WHERE (  `id` = 750022 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-22 08:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000470s ]
[2025-07-22 08:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000295s ]
[2025-07-22 08:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299560s ]
[2025-07-22 08:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000330s ]
[2025-07-22 08:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000300s ]
[2025-07-22 08:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000286s ]
[2025-07-22 08:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001537s ]
[2025-07-22 08:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-22 08:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000195s ]
[2025-07-22 08:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000146s ]
[2025-07-22 08:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-22 08:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-22 08:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-22 08:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753143003  WHERE (  `id` = 750023 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-22 08:30:01][sql] CONNECT:[ UseTime:0.000391s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 08:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 07' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.004510s ]
[2025-07-22 08:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 07' [ RunTime:0.006893s ]
[2025-07-22 08:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.003963s ]
[2025-07-22 08:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 07' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000368s ]
[2025-07-22 08:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 07' [ RunTime:0.004357s ]
[2025-07-22 09:10:01][sql] CONNECT:[ UseTime:0.000281s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 09:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000297s ]
[2025-07-22 09:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000217s ]
[2025-07-22 09:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000251s ]
[2025-07-22 09:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000235s ]
[2025-07-22 09:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000279s ]
[2025-07-22 09:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002592s ]
[2025-07-22 09:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000206s ]
[2025-07-22 09:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000162s ]
[2025-07-22 09:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000188s ]
[2025-07-22 09:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000255s ]
[2025-07-22 09:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.306405s ]
[2025-07-22 09:10:02][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000477s ]
[2025-07-22 09:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000370s ]
[2025-07-22 09:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000199s ]
[2025-07-22 09:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000221s ]
[2025-07-22 09:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000171s ]
[2025-07-22 09:10:02][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000226s ]
[2025-07-22 09:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001514s ]
[2025-07-22 09:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000222s ]
[2025-07-22 09:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000169s ]
[2025-07-22 09:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000216s ]
[2025-07-22 09:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000195s ]
[2025-07-22 09:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000211s ]
[2025-07-22 09:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000145s ]
[2025-07-22 09:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-22 09:10:02][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000166s ]
[2025-07-22 09:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-22 09:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-22 09:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753146602  WHERE (  `id` = 750018 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000119s ]
[2025-07-22 09:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000159s ]
[2025-07-22 09:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000251s ]
[2025-07-22 09:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299499s ]
[2025-07-22 09:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000310s ]
[2025-07-22 09:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000354s ]
[2025-07-22 09:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000236s ]
[2025-07-22 09:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001497s ]
[2025-07-22 09:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000334s ]
[2025-07-22 09:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000310s ]
[2025-07-22 09:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000156s ]
[2025-07-22 09:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000150s ]
[2025-07-22 09:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000145s ]
[2025-07-22 09:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000213s ]
[2025-07-22 09:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753146602  WHERE (  `id` = 750019 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000160s ]
[2025-07-22 09:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000201s ]
[2025-07-22 09:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000131s ]
[2025-07-22 09:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300500s ]
[2025-07-22 09:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000366s ]
[2025-07-22 09:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000331s ]
[2025-07-22 09:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000369s ]
[2025-07-22 09:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001574s ]
[2025-07-22 09:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000168s ]
[2025-07-22 09:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000201s ]
[2025-07-22 09:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000176s ]
[2025-07-22 09:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000153s ]
[2025-07-22 09:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000148s ]
[2025-07-22 09:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000165s ]
[2025-07-22 09:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753146602  WHERE (  `id` = 750020 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000174s ]
[2025-07-22 09:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000256s ]
[2025-07-22 09:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000261s ]
[2025-07-22 09:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299969s ]
[2025-07-22 09:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000183s ]
[2025-07-22 09:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-22 09:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-22 09:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001456s ]
[2025-07-22 09:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000105s ]
[2025-07-22 09:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000133s ]
[2025-07-22 09:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000138s ]
[2025-07-22 09:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-22 09:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 09:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-22 09:10:03][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753146603  WHERE (  `id` = 750021 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000126s ]
[2025-07-22 09:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000295s ]
[2025-07-22 09:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000203s ]
[2025-07-22 09:10:03][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000242s ]
[2025-07-22 09:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000185s ]
[2025-07-22 09:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000168s ]
[2025-07-22 09:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000171s ]
[2025-07-22 09:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299871s ]
[2025-07-22 09:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000350s ]
[2025-07-22 09:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000275s ]
[2025-07-22 09:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000206s ]
[2025-07-22 09:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001552s ]
[2025-07-22 09:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000289s ]
[2025-07-22 09:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000229s ]
[2025-07-22 09:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000101s ]
[2025-07-22 09:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-22 09:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 09:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000099s ]
[2025-07-22 09:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753146603  WHERE (  `id` = 750022 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-22 09:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000332s ]
[2025-07-22 09:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000248s ]
[2025-07-22 09:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299345s ]
[2025-07-22 09:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000338s ]
[2025-07-22 09:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000151s ]
[2025-07-22 09:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000179s ]
[2025-07-22 09:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001554s ]
[2025-07-22 09:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000247s ]
[2025-07-22 09:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000146s ]
[2025-07-22 09:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000095s ]
[2025-07-22 09:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000103s ]
[2025-07-22 09:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 09:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-22 09:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753146603  WHERE (  `id` = 750023 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-22 09:30:01][sql] CONNECT:[ UseTime:0.000387s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 09:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 08' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.043496s ]
[2025-07-22 09:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 08' [ RunTime:0.007692s ]
[2025-07-22 09:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004210s ]
[2025-07-22 09:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 08' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.001836s ]
[2025-07-22 09:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 08' [ RunTime:0.005930s ]
[2025-07-22 10:10:01][sql] CONNECT:[ UseTime:0.000379s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000300s ]
[2025-07-22 10:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000173s ]
[2025-07-22 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000187s ]
[2025-07-22 10:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000157s ]
[2025-07-22 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000275s ]
[2025-07-22 10:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002606s ]
[2025-07-22 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000185s ]
[2025-07-22 10:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000114s ]
[2025-07-22 10:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000279s ]
[2025-07-22 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000295s ]
[2025-07-22 10:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.303881s ]
[2025-07-22 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000477s ]
[2025-07-22 10:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000365s ]
[2025-07-22 10:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000239s ]
[2025-07-22 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000220s ]
[2025-07-22 10:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000174s ]
[2025-07-22 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000228s ]
[2025-07-22 10:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001526s ]
[2025-07-22 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000223s ]
[2025-07-22 10:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000165s ]
[2025-07-22 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000218s ]
[2025-07-22 10:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000195s ]
[2025-07-22 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000210s ]
[2025-07-22 10:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000147s ]
[2025-07-22 10:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-22 10:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000211s ]
[2025-07-22 10:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000150s ]
[2025-07-22 10:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-22 10:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753150201  WHERE (  `id` = 750018 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000121s ]
[2025-07-22 10:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000158s ]
[2025-07-22 10:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000193s ]
[2025-07-22 10:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299308s ]
[2025-07-22 10:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000278s ]
[2025-07-22 10:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000260s ]
[2025-07-22 10:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000246s ]
[2025-07-22 10:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001482s ]
[2025-07-22 10:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-22 10:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000135s ]
[2025-07-22 10:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-22 10:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000270s ]
[2025-07-22 10:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000263s ]
[2025-07-22 10:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000235s ]
[2025-07-22 10:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753150201  WHERE (  `id` = 750019 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000195s ]
[2025-07-22 10:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000181s ]
[2025-07-22 10:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000180s ]
[2025-07-22 10:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299374s ]
[2025-07-22 10:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000294s ]
[2025-07-22 10:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000163s ]
[2025-07-22 10:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000115s ]
[2025-07-22 10:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001463s ]
[2025-07-22 10:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000278s ]
[2025-07-22 10:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000200s ]
[2025-07-22 10:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000094s ]
[2025-07-22 10:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000103s ]
[2025-07-22 10:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-22 10:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000097s ]
[2025-07-22 10:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753150202  WHERE (  `id` = 750020 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-22 10:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000363s ]
[2025-07-22 10:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000248s ]
[2025-07-22 10:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299525s ]
[2025-07-22 10:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000263s ]
[2025-07-22 10:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000259s ]
[2025-07-22 10:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000315s ]
[2025-07-22 10:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001521s ]
[2025-07-22 10:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-22 10:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000133s ]
[2025-07-22 10:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-22 10:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000159s ]
[2025-07-22 10:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000166s ]
[2025-07-22 10:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000107s ]
[2025-07-22 10:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753150202  WHERE (  `id` = 750021 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-22 10:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000290s ]
[2025-07-22 10:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000127s ]
[2025-07-22 10:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000230s ]
[2025-07-22 10:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000127s ]
[2025-07-22 10:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000107s ]
[2025-07-22 10:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000104s ]
[2025-07-22 10:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299390s ]
[2025-07-22 10:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000215s ]
[2025-07-22 10:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000215s ]
[2025-07-22 10:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000314s ]
[2025-07-22 10:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001522s ]
[2025-07-22 10:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000244s ]
[2025-07-22 10:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000196s ]
[2025-07-22 10:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000144s ]
[2025-07-22 10:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000140s ]
[2025-07-22 10:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000138s ]
[2025-07-22 10:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000099s ]
[2025-07-22 10:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753150202  WHERE (  `id` = 750022 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-22 10:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000486s ]
[2025-07-22 10:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000249s ]
[2025-07-22 10:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299454s ]
[2025-07-22 10:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000306s ]
[2025-07-22 10:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000255s ]
[2025-07-22 10:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000186s ]
[2025-07-22 10:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001483s ]
[2025-07-22 10:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000144s ]
[2025-07-22 10:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000178s ]
[2025-07-22 10:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000097s ]
[2025-07-22 10:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-22 10:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 10:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-22 10:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753150203  WHERE (  `id` = 750023 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-22 10:30:01][sql] CONNECT:[ UseTime:0.000281s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 10:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 09' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.004582s ]
[2025-07-22 10:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 09' [ RunTime:0.005791s ]
[2025-07-22 10:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.003281s ]
[2025-07-22 10:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 09' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000585s ]
[2025-07-22 10:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 09' [ RunTime:0.002122s ]
[2025-07-22 11:10:01][sql] CONNECT:[ UseTime:0.000468s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 11:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000391s ]
[2025-07-22 11:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000234s ]
[2025-07-22 11:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000317s ]
[2025-07-22 11:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000340s ]
[2025-07-22 11:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000374s ]
[2025-07-22 11:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002641s ]
[2025-07-22 11:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000356s ]
[2025-07-22 11:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000168s ]
[2025-07-22 11:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000188s ]
[2025-07-22 11:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000256s ]
[2025-07-22 11:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.303428s ]
[2025-07-22 11:10:02][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000443s ]
[2025-07-22 11:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000325s ]
[2025-07-22 11:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000246s ]
[2025-07-22 11:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000221s ]
[2025-07-22 11:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000166s ]
[2025-07-22 11:10:02][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000234s ]
[2025-07-22 11:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001491s ]
[2025-07-22 11:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000164s ]
[2025-07-22 11:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-22 11:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000159s ]
[2025-07-22 11:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000158s ]
[2025-07-22 11:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000149s ]
[2025-07-22 11:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000094s ]
[2025-07-22 11:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-22 11:10:02][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000147s ]
[2025-07-22 11:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-22 11:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000092s ]
[2025-07-22 11:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753153802  WHERE (  `id` = 750018 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000118s ]
[2025-07-22 11:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000202s ]
[2025-07-22 11:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000311s ]
[2025-07-22 11:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.303146s ]
[2025-07-22 11:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000382s ]
[2025-07-22 11:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000345s ]
[2025-07-22 11:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000293s ]
[2025-07-22 11:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001529s ]
[2025-07-22 11:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-22 11:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000133s ]
[2025-07-22 11:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000131s ]
[2025-07-22 11:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000091s ]
[2025-07-22 11:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 11:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000100s ]
[2025-07-22 11:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753153802  WHERE (  `id` = 750019 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000120s ]
[2025-07-22 11:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000197s ]
[2025-07-22 11:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000276s ]
[2025-07-22 11:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.303442s ]
[2025-07-22 11:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000305s ]
[2025-07-22 11:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000232s ]
[2025-07-22 11:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000183s ]
[2025-07-22 11:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001531s ]
[2025-07-22 11:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-22 11:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-22 11:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-22 11:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-22 11:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 11:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-22 11:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753153802  WHERE (  `id` = 750020 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-22 11:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000257s ]
[2025-07-22 11:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000133s ]
[2025-07-22 11:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.303809s ]
[2025-07-22 11:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000240s ]
[2025-07-22 11:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000159s ]
[2025-07-22 11:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000298s ]
[2025-07-22 11:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001526s ]
[2025-07-22 11:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000131s ]
[2025-07-22 11:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000138s ]
[2025-07-22 11:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000092s ]
[2025-07-22 11:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-22 11:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-22 11:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000100s ]
[2025-07-22 11:10:03][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753153803  WHERE (  `id` = 750021 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000122s ]
[2025-07-22 11:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000517s ]
[2025-07-22 11:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000161s ]
[2025-07-22 11:10:03][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000314s ]
[2025-07-22 11:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000162s ]
[2025-07-22 11:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000130s ]
[2025-07-22 11:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000125s ]
[2025-07-22 11:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.306112s ]
[2025-07-22 11:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000389s ]
[2025-07-22 11:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000337s ]
[2025-07-22 11:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000340s ]
[2025-07-22 11:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001530s ]
[2025-07-22 11:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-22 11:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000175s ]
[2025-07-22 11:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000170s ]
[2025-07-22 11:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000101s ]
[2025-07-22 11:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-22 11:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000098s ]
[2025-07-22 11:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753153803  WHERE (  `id` = 750022 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000124s ]
[2025-07-22 11:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000339s ]
[2025-07-22 11:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000257s ]
[2025-07-22 11:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.303511s ]
[2025-07-22 11:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000317s ]
[2025-07-22 11:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000258s ]
[2025-07-22 11:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000199s ]
[2025-07-22 11:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001461s ]
[2025-07-22 11:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000183s ]
[2025-07-22 11:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000202s ]
[2025-07-22 11:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000092s ]
[2025-07-22 11:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-22 11:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-22 11:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000097s ]
[2025-07-22 11:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753153803  WHERE (  `id` = 750023 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-22 11:30:01][sql] CONNECT:[ UseTime:0.000453s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 11:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 10' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.005430s ]
[2025-07-22 11:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 10' [ RunTime:0.006362s ]
[2025-07-22 11:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.003924s ]
[2025-07-22 11:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 10' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000549s ]
[2025-07-22 11:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 10' [ RunTime:0.002059s ]
[2025-07-22 12:10:01][sql] CONNECT:[ UseTime:0.000439s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000484s ]
[2025-07-22 12:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000224s ]
[2025-07-22 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000243s ]
[2025-07-22 12:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000173s ]
[2025-07-22 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000261s ]
[2025-07-22 12:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002498s ]
[2025-07-22 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000225s ]
[2025-07-22 12:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000164s ]
[2025-07-22 12:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000184s ]
[2025-07-22 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000212s ]
[2025-07-22 12:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300729s ]
[2025-07-22 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000417s ]
[2025-07-22 12:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000196s ]
[2025-07-22 12:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000180s ]
[2025-07-22 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000210s ]
[2025-07-22 12:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000121s ]
[2025-07-22 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000168s ]
[2025-07-22 12:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001442s ]
[2025-07-22 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000157s ]
[2025-07-22 12:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-22 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000161s ]
[2025-07-22 12:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000140s ]
[2025-07-22 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000146s ]
[2025-07-22 12:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000092s ]
[2025-07-22 12:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-22 12:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000148s ]
[2025-07-22 12:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-22 12:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000089s ]
[2025-07-22 12:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753157401  WHERE (  `id` = 750018 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000117s ]
[2025-07-22 12:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000124s ]
[2025-07-22 12:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000131s ]
[2025-07-22 12:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300216s ]
[2025-07-22 12:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000198s ]
[2025-07-22 12:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000160s ]
[2025-07-22 12:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000175s ]
[2025-07-22 12:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001494s ]
[2025-07-22 12:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-22 12:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000134s ]
[2025-07-22 12:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-22 12:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-22 12:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-22 12:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000091s ]
[2025-07-22 12:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753157402  WHERE (  `id` = 750019 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-22 12:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000300s ]
[2025-07-22 12:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000188s ]
[2025-07-22 12:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300317s ]
[2025-07-22 12:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000273s ]
[2025-07-22 12:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000235s ]
[2025-07-22 12:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000123s ]
[2025-07-22 12:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001424s ]
[2025-07-22 12:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000106s ]
[2025-07-22 12:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000132s ]
[2025-07-22 12:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-22 12:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-22 12:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-22 12:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000102s ]
[2025-07-22 12:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753157402  WHERE (  `id` = 750020 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-22 12:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000258s ]
[2025-07-22 12:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000186s ]
[2025-07-22 12:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300380s ]
[2025-07-22 12:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000132s ]
[2025-07-22 12:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000223s ]
[2025-07-22 12:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000316s ]
[2025-07-22 12:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001515s ]
[2025-07-22 12:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000165s ]
[2025-07-22 12:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000200s ]
[2025-07-22 12:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000148s ]
[2025-07-22 12:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000150s ]
[2025-07-22 12:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000145s ]
[2025-07-22 12:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000101s ]
[2025-07-22 12:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753157402  WHERE (  `id` = 750021 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-22 12:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000462s ]
[2025-07-22 12:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000280s ]
[2025-07-22 12:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000279s ]
[2025-07-22 12:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000187s ]
[2025-07-22 12:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000174s ]
[2025-07-22 12:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000168s ]
[2025-07-22 12:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300688s ]
[2025-07-22 12:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000220s ]
[2025-07-22 12:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000251s ]
[2025-07-22 12:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000165s ]
[2025-07-22 12:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001431s ]
[2025-07-22 12:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-22 12:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000133s ]
[2025-07-22 12:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-22 12:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-22 12:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 12:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000098s ]
[2025-07-22 12:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753157403  WHERE (  `id` = 750022 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-22 12:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000389s ]
[2025-07-22 12:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000250s ]
[2025-07-22 12:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300909s ]
[2025-07-22 12:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000343s ]
[2025-07-22 12:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000182s ]
[2025-07-22 12:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000194s ]
[2025-07-22 12:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001487s ]
[2025-07-22 12:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000196s ]
[2025-07-22 12:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000181s ]
[2025-07-22 12:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000097s ]
[2025-07-22 12:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-22 12:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 12:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000098s ]
[2025-07-22 12:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753157403  WHERE (  `id` = 750023 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-22 12:30:01][sql] CONNECT:[ UseTime:0.000448s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 12:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 11' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.005315s ]
[2025-07-22 12:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 11' [ RunTime:0.007452s ]
[2025-07-22 12:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004557s ]
[2025-07-22 12:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 11' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000597s ]
[2025-07-22 12:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 11' [ RunTime:0.002309s ]
[2025-07-22 13:10:01][sql] CONNECT:[ UseTime:0.000393s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 13:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000458s ]
[2025-07-22 13:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000190s ]
[2025-07-22 13:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000194s ]
[2025-07-22 13:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` IN ('') OR `type` <= 1 [ RunTime:0.000120s ]
[2025-07-22 13:30:01][sql] CONNECT:[ UseTime:0.000287s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 13:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 12' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.006315s ]
[2025-07-22 13:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 12' [ RunTime:0.006228s ]
[2025-07-22 13:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004965s ]
[2025-07-22 13:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 12' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000758s ]
[2025-07-22 13:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 12' [ RunTime:0.002298s ]
[2025-07-22 14:10:01][sql] CONNECT:[ UseTime:0.000435s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 14:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000364s ]
[2025-07-22 14:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000181s ]
[2025-07-22 14:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000187s ]
[2025-07-22 14:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` IN ('') OR `type` <= 1 [ RunTime:0.000308s ]
[2025-07-22 14:30:01][sql] CONNECT:[ UseTime:0.000290s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 14:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 13' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.000248s ]
[2025-07-22 14:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 13' [ RunTime:0.002296s ]
[2025-07-22 14:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.001999s ]
[2025-07-22 14:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 13' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000404s ]
[2025-07-22 14:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 13' [ RunTime:0.002287s ]
[2025-07-22 15:10:01][sql] CONNECT:[ UseTime:0.000421s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 15:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000321s ]
[2025-07-22 15:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000382s ]
[2025-07-22 15:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000332s ]
[2025-07-22 15:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` IN ('') OR `type` <= 1 [ RunTime:0.000245s ]
[2025-07-22 15:30:01][sql] CONNECT:[ UseTime:0.000392s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 15:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 14' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.005135s ]
[2025-07-22 15:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 14' [ RunTime:0.008189s ]
[2025-07-22 15:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004281s ]
[2025-07-22 15:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 14' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.001051s ]
[2025-07-22 15:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 14' [ RunTime:0.005064s ]
[2025-07-22 16:10:01][sql] CONNECT:[ UseTime:0.000405s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 16:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000478s ]
[2025-07-22 16:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000251s ]
[2025-07-22 16:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000198s ]
[2025-07-22 16:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` IN ('') OR `type` <= 1 [ RunTime:0.000120s ]
[2025-07-22 16:30:01][sql] CONNECT:[ UseTime:0.000473s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 16:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 15' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.005545s ]
[2025-07-22 16:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 15' [ RunTime:0.006956s ]
[2025-07-22 16:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.003752s ]
[2025-07-22 16:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 15' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000347s ]
[2025-07-22 16:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 15' [ RunTime:0.005179s ]
[2025-07-22 17:10:01][sql] CONNECT:[ UseTime:0.000429s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 17:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000336s ]
[2025-07-22 17:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000239s ]
[2025-07-22 17:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000414s ]
[2025-07-22 17:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000373s ]
[2025-07-22 17:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000393s ]
[2025-07-22 17:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002601s ]
[2025-07-22 17:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000309s ]
[2025-07-22 17:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000119s ]
[2025-07-22 17:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000133s ]
[2025-07-22 17:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000199s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300060s ]
[2025-07-22 17:10:02][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000414s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000283s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000169s ]
[2025-07-22 17:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000222s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000173s ]
[2025-07-22 17:10:02][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000241s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001519s ]
[2025-07-22 17:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000327s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000174s ]
[2025-07-22 17:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000217s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000197s ]
[2025-07-22 17:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000210s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000101s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-22 17:10:02][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000151s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000120s ]
[2025-07-22 17:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753175402  WHERE (  `id` = 750018 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000122s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000121s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000133s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299746s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000316s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000157s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000215s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001530s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000165s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000192s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000145s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000148s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000138s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000151s ]
[2025-07-22 17:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753175402  WHERE (  `id` = 750019 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000176s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000182s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000314s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299420s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000318s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000317s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000292s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001586s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000165s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000225s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000093s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000097s ]
[2025-07-22 17:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753175402  WHERE (  `id` = 750020 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000294s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000348s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000247s ]
[2025-07-22 17:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000287s ]
[2025-07-22 17:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000187s ]
[2025-07-22 17:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000213s ]
[2025-07-22 17:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000154s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299363s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000202s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000099s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001436s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000154s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000138s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000125s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000093s ]
[2025-07-22 17:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000097s ]
[2025-07-22 17:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753175402  WHERE (  `id` = 750021 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000379s ]
[2025-07-22 17:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000329s ]
[2025-07-22 17:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000189s ]
[2025-07-22 17:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000166s ]
[2025-07-22 17:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000158s ]
[2025-07-22 17:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299650s ]
[2025-07-22 17:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000288s ]
[2025-07-22 17:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000193s ]
[2025-07-22 17:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000163s ]
[2025-07-22 17:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001418s ]
[2025-07-22 17:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000111s ]
[2025-07-22 17:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000141s ]
[2025-07-22 17:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-22 17:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-22 17:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-22 17:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-22 17:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753175403  WHERE (  `id` = 750022 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-22 17:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000351s ]
[2025-07-22 17:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000208s ]
[2025-07-22 17:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000210s ]
[2025-07-22 17:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000286s ]
[2025-07-22 17:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000166s ]
[2025-07-22 17:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299754s ]
[2025-07-22 17:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000320s ]
[2025-07-22 17:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000159s ]
[2025-07-22 17:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-22 17:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001442s ]
[2025-07-22 17:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000104s ]
[2025-07-22 17:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000133s ]
[2025-07-22 17:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-22 17:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-22 17:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-22 17:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000091s ]
[2025-07-22 17:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753175403  WHERE (  `id` = 750023 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000103s ]
[2025-07-22 17:30:01][sql] CONNECT:[ UseTime:0.000448s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 17:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 16' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.005068s ]
[2025-07-22 17:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 16' [ RunTime:0.007327s ]
[2025-07-22 17:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004242s ]
[2025-07-22 17:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 16' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000650s ]
[2025-07-22 17:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 16' [ RunTime:0.004313s ]
[2025-07-22 18:10:01][sql] CONNECT:[ UseTime:0.000403s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 18:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000495s ]
[2025-07-22 18:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000196s ]
[2025-07-22 18:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000188s ]
[2025-07-22 18:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000120s ]
[2025-07-22 18:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000241s ]
[2025-07-22 18:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002598s ]
[2025-07-22 18:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000186s ]
[2025-07-22 18:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000114s ]
[2025-07-22 18:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000131s ]
[2025-07-22 18:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000204s ]
[2025-07-22 18:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300673s ]
[2025-07-22 18:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000479s ]
[2025-07-22 18:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000366s ]
[2025-07-22 18:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000356s ]
[2025-07-22 18:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000252s ]
[2025-07-22 18:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000172s ]
[2025-07-22 18:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000238s ]
[2025-07-22 18:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001512s ]
[2025-07-22 18:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000215s ]
[2025-07-22 18:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000170s ]
[2025-07-22 18:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000228s ]
[2025-07-22 18:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000212s ]
[2025-07-22 18:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000217s ]
[2025-07-22 18:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000156s ]
[2025-07-22 18:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000151s ]
[2025-07-22 18:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000219s ]
[2025-07-22 18:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000144s ]
[2025-07-22 18:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-22 18:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753179001  WHERE (  `id` = 750018 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000119s ]
[2025-07-22 18:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000163s ]
[2025-07-22 18:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000135s ]
[2025-07-22 18:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300220s ]
[2025-07-22 18:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000193s ]
[2025-07-22 18:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000181s ]
[2025-07-22 18:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000202s ]
[2025-07-22 18:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001446s ]
[2025-07-22 18:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000103s ]
[2025-07-22 18:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000131s ]
[2025-07-22 18:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-22 18:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000098s ]
[2025-07-22 18:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000147s ]
[2025-07-22 18:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000157s ]
[2025-07-22 18:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753179001  WHERE (  `id` = 750019 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000166s ]
[2025-07-22 18:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000182s ]
[2025-07-22 18:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000245s ]
[2025-07-22 18:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300237s ]
[2025-07-22 18:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000132s ]
[2025-07-22 18:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-22 18:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000301s ]
[2025-07-22 18:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001554s ]
[2025-07-22 18:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000170s ]
[2025-07-22 18:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000209s ]
[2025-07-22 18:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000167s ]
[2025-07-22 18:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000150s ]
[2025-07-22 18:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000146s ]
[2025-07-22 18:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000103s ]
[2025-07-22 18:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753179002  WHERE (  `id` = 750020 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-22 18:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000251s ]
[2025-07-22 18:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000249s ]
[2025-07-22 18:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000280s ]
[2025-07-22 18:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000367s ]
[2025-07-22 18:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000313s ]
[2025-07-22 18:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000220s ]
[2025-07-22 18:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301239s ]
[2025-07-22 18:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000277s ]
[2025-07-22 18:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000150s ]
[2025-07-22 18:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000117s ]
[2025-07-22 18:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001494s ]
[2025-07-22 18:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000105s ]
[2025-07-22 18:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000134s ]
[2025-07-22 18:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-22 18:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-22 18:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000251s ]
[2025-07-22 18:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000226s ]
[2025-07-22 18:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753179002  WHERE (  `id` = 750021 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000166s ]
[2025-07-22 18:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000336s ]
[2025-07-22 18:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000189s ]
[2025-07-22 18:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000175s ]
[2025-07-22 18:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000168s ]
[2025-07-22 18:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000159s ]
[2025-07-22 18:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301185s ]
[2025-07-22 18:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000145s ]
[2025-07-22 18:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000159s ]
[2025-07-22 18:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000352s ]
[2025-07-22 18:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001544s ]
[2025-07-22 18:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000166s ]
[2025-07-22 18:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000199s ]
[2025-07-22 18:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000156s ]
[2025-07-22 18:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000151s ]
[2025-07-22 18:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-22 18:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000098s ]
[2025-07-22 18:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753179002  WHERE (  `id` = 750022 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-22 18:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000394s ]
[2025-07-22 18:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000283s ]
[2025-07-22 18:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000128s ]
[2025-07-22 18:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000185s ]
[2025-07-22 18:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000163s ]
[2025-07-22 18:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300791s ]
[2025-07-22 18:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000273s ]
[2025-07-22 18:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000099s ]
[2025-07-22 18:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-22 18:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001436s ]
[2025-07-22 18:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000153s ]
[2025-07-22 18:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000138s ]
[2025-07-22 18:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-22 18:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-22 18:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-22 18:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-22 18:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753179003  WHERE (  `id` = 750023 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000106s ]
[2025-07-22 18:30:01][sql] CONNECT:[ UseTime:0.000296s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 18:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 17' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.005473s ]
[2025-07-22 18:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 17' [ RunTime:0.007643s ]
[2025-07-22 18:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004300s ]
[2025-07-22 18:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 17' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000492s ]
[2025-07-22 18:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 17' [ RunTime:0.005211s ]
[2025-07-22 19:10:01][sql] CONNECT:[ UseTime:0.000405s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 19:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000328s ]
[2025-07-22 19:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000201s ]
[2025-07-22 19:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000409s ]
[2025-07-22 19:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000331s ]
[2025-07-22 19:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000285s ]
[2025-07-22 19:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002654s ]
[2025-07-22 19:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000341s ]
[2025-07-22 19:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000175s ]
[2025-07-22 19:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000264s ]
[2025-07-22 19:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000302s ]
[2025-07-22 19:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302089s ]
[2025-07-22 19:10:02][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000487s ]
[2025-07-22 19:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000373s ]
[2025-07-22 19:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000320s ]
[2025-07-22 19:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000231s ]
[2025-07-22 19:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000185s ]
[2025-07-22 19:10:02][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000233s ]
[2025-07-22 19:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001500s ]
[2025-07-22 19:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000219s ]
[2025-07-22 19:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000166s ]
[2025-07-22 19:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000173s ]
[2025-07-22 19:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000143s ]
[2025-07-22 19:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000151s ]
[2025-07-22 19:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000094s ]
[2025-07-22 19:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-22 19:10:02][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000154s ]
[2025-07-22 19:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000091s ]
[2025-07-22 19:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-22 19:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753182602  WHERE (  `id` = 750018 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000122s ]
[2025-07-22 19:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000123s ]
[2025-07-22 19:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000133s ]
[2025-07-22 19:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299732s ]
[2025-07-22 19:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000226s ]
[2025-07-22 19:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000179s ]
[2025-07-22 19:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000200s ]
[2025-07-22 19:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001429s ]
[2025-07-22 19:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000106s ]
[2025-07-22 19:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000133s ]
[2025-07-22 19:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-22 19:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-22 19:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-22 19:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000088s ]
[2025-07-22 19:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753182602  WHERE (  `id` = 750019 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000184s ]
[2025-07-22 19:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000182s ]
[2025-07-22 19:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000252s ]
[2025-07-22 19:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300156s ]
[2025-07-22 19:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000351s ]
[2025-07-22 19:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000317s ]
[2025-07-22 19:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000201s ]
[2025-07-22 19:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001555s ]
[2025-07-22 19:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000318s ]
[2025-07-22 19:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000316s ]
[2025-07-22 19:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000159s ]
[2025-07-22 19:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000157s ]
[2025-07-22 19:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-22 19:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000099s ]
[2025-07-22 19:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753182602  WHERE (  `id` = 750020 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-22 19:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000276s ]
[2025-07-22 19:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000131s ]
[2025-07-22 19:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000235s ]
[2025-07-22 19:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000257s ]
[2025-07-22 19:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000219s ]
[2025-07-22 19:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000217s ]
[2025-07-22 19:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300173s ]
[2025-07-22 19:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000222s ]
[2025-07-22 19:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000194s ]
[2025-07-22 19:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000166s ]
[2025-07-22 19:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001438s ]
[2025-07-22 19:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000321s ]
[2025-07-22 19:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000347s ]
[2025-07-22 19:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000299s ]
[2025-07-22 19:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000302s ]
[2025-07-22 19:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000171s ]
[2025-07-22 19:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000155s ]
[2025-07-22 19:10:03][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753182603  WHERE (  `id` = 750021 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000169s ]
[2025-07-22 19:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000297s ]
[2025-07-22 19:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000311s ]
[2025-07-22 19:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000262s ]
[2025-07-22 19:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000117s ]
[2025-07-22 19:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000165s ]
[2025-07-22 19:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299926s ]
[2025-07-22 19:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000308s ]
[2025-07-22 19:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000277s ]
[2025-07-22 19:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000185s ]
[2025-07-22 19:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001546s ]
[2025-07-22 19:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000166s ]
[2025-07-22 19:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000199s ]
[2025-07-22 19:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000152s ]
[2025-07-22 19:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000149s ]
[2025-07-22 19:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000145s ]
[2025-07-22 19:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000160s ]
[2025-07-22 19:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753182603  WHERE (  `id` = 750022 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000168s ]
[2025-07-22 19:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000347s ]
[2025-07-22 19:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000248s ]
[2025-07-22 19:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000168s ]
[2025-07-22 19:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000162s ]
[2025-07-22 19:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000163s ]
[2025-07-22 19:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300164s ]
[2025-07-22 19:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000198s ]
[2025-07-22 19:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000292s ]
[2025-07-22 19:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000322s ]
[2025-07-22 19:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001593s ]
[2025-07-22 19:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000171s ]
[2025-07-22 19:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000201s ]
[2025-07-22 19:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000151s ]
[2025-07-22 19:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-22 19:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 19:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-22 19:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753182603  WHERE (  `id` = 750023 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-22 19:30:01][sql] CONNECT:[ UseTime:0.000338s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 19:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 18' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.010357s ]
[2025-07-22 19:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 18' [ RunTime:0.010340s ]
[2025-07-22 19:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.006051s ]
[2025-07-22 19:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 18' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000661s ]
[2025-07-22 19:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 18' [ RunTime:0.004332s ]
[2025-07-22 20:10:01][sql] CONNECT:[ UseTime:0.000456s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000480s ]
[2025-07-22 20:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000241s ]
[2025-07-22 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000247s ]
[2025-07-22 20:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000172s ]
[2025-07-22 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000284s ]
[2025-07-22 20:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002578s ]
[2025-07-22 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000266s ]
[2025-07-22 20:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000162s ]
[2025-07-22 20:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000135s ]
[2025-07-22 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000205s ]
[2025-07-22 20:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.298553s ]
[2025-07-22 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000393s ]
[2025-07-22 20:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000244s ]
[2025-07-22 20:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000163s ]
[2025-07-22 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000221s ]
[2025-07-22 20:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000161s ]
[2025-07-22 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000265s ]
[2025-07-22 20:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001662s ]
[2025-07-22 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000219s ]
[2025-07-22 20:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000170s ]
[2025-07-22 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000216s ]
[2025-07-22 20:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000194s ]
[2025-07-22 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000210s ]
[2025-07-22 20:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000117s ]
[2025-07-22 20:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-22 20:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000156s ]
[2025-07-22 20:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-22 20:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000092s ]
[2025-07-22 20:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753186201  WHERE (  `id` = 750018 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000117s ]
[2025-07-22 20:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000161s ]
[2025-07-22 20:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000187s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.298683s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000290s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000298s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000308s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001507s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000170s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000279s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000269s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000266s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000148s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000152s ]
[2025-07-22 20:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753186202  WHERE (  `id` = 750019 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000162s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000266s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000150s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301312s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000395s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000335s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000224s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001580s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000147s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000219s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000097s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-22 20:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753186202  WHERE (  `id` = 750020 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000119s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000252s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000197s ]
[2025-07-22 20:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000231s ]
[2025-07-22 20:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000226s ]
[2025-07-22 20:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000218s ]
[2025-07-22 20:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000220s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299237s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000227s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000196s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000202s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001447s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000105s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000133s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000078s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000093s ]
[2025-07-22 20:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753186202  WHERE (  `id` = 750021 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000109s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000466s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000136s ]
[2025-07-22 20:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000117s ]
[2025-07-22 20:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000103s ]
[2025-07-22 20:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000100s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302148s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000354s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000310s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000338s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001590s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000172s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000195s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000254s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000154s ]
[2025-07-22 20:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000148s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000160s ]
[2025-07-22 20:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753186202  WHERE (  `id` = 750022 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000196s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000448s ]
[2025-07-22 20:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000193s ]
[2025-07-22 20:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000122s ]
[2025-07-22 20:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000103s ]
[2025-07-22 20:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000125s ]
[2025-07-22 20:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299735s ]
[2025-07-22 20:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000322s ]
[2025-07-22 20:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000309s ]
[2025-07-22 20:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000177s ]
[2025-07-22 20:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001564s ]
[2025-07-22 20:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000166s ]
[2025-07-22 20:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000190s ]
[2025-07-22 20:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000143s ]
[2025-07-22 20:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000140s ]
[2025-07-22 20:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-22 20:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000172s ]
[2025-07-22 20:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753186203  WHERE (  `id` = 750023 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000162s ]
[2025-07-22 20:30:01][sql] CONNECT:[ UseTime:0.000362s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 20:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 19' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.007871s ]
[2025-07-22 20:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 19' [ RunTime:0.011633s ]
[2025-07-22 20:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004017s ]
[2025-07-22 20:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 19' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000367s ]
[2025-07-22 20:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 19' [ RunTime:0.002072s ]
[2025-07-22 21:10:01][sql] CONNECT:[ UseTime:0.000297s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 21:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000469s ]
[2025-07-22 21:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000360s ]
[2025-07-22 21:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000397s ]
[2025-07-22 21:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000220s ]
[2025-07-22 21:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000279s ]
[2025-07-22 21:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002571s ]
[2025-07-22 21:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000176s ]
[2025-07-22 21:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000110s ]
[2025-07-22 21:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000135s ]
[2025-07-22 21:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000201s ]
[2025-07-22 21:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300427s ]
[2025-07-22 21:10:02][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000264s ]
[2025-07-22 21:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000166s ]
[2025-07-22 21:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000155s ]
[2025-07-22 21:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000352s ]
[2025-07-22 21:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000172s ]
[2025-07-22 21:10:02][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000225s ]
[2025-07-22 21:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001514s ]
[2025-07-22 21:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000214s ]
[2025-07-22 21:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000161s ]
[2025-07-22 21:10:02][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000219s ]
[2025-07-22 21:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000149s ]
[2025-07-22 21:10:02][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000155s ]
[2025-07-22 21:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000150s ]
[2025-07-22 21:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000143s ]
[2025-07-22 21:10:02][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000213s ]
[2025-07-22 21:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000142s ]
[2025-07-22 21:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000094s ]
[2025-07-22 21:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753189802  WHERE (  `id` = 750018 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000118s ]
[2025-07-22 21:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000162s ]
[2025-07-22 21:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000176s ]
[2025-07-22 21:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300449s ]
[2025-07-22 21:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000349s ]
[2025-07-22 21:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000348s ]
[2025-07-22 21:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000296s ]
[2025-07-22 21:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001519s ]
[2025-07-22 21:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000167s ]
[2025-07-22 21:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000200s ]
[2025-07-22 21:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000095s ]
[2025-07-22 21:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-22 21:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-22 21:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000172s ]
[2025-07-22 21:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753189802  WHERE (  `id` = 750019 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000165s ]
[2025-07-22 21:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000195s ]
[2025-07-22 21:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000255s ]
[2025-07-22 21:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300807s ]
[2025-07-22 21:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000257s ]
[2025-07-22 21:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000154s ]
[2025-07-22 21:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000113s ]
[2025-07-22 21:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001461s ]
[2025-07-22 21:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000187s ]
[2025-07-22 21:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000145s ]
[2025-07-22 21:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000097s ]
[2025-07-22 21:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000151s ]
[2025-07-22 21:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000146s ]
[2025-07-22 21:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000162s ]
[2025-07-22 21:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753189802  WHERE (  `id` = 750020 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000164s ]
[2025-07-22 21:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000362s ]
[2025-07-22 21:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000222s ]
[2025-07-22 21:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000232s ]
[2025-07-22 21:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000192s ]
[2025-07-22 21:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000163s ]
[2025-07-22 21:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000157s ]
[2025-07-22 21:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.302880s ]
[2025-07-22 21:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000213s ]
[2025-07-22 21:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000171s ]
[2025-07-22 21:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000163s ]
[2025-07-22 21:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001440s ]
[2025-07-22 21:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000105s ]
[2025-07-22 21:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000139s ]
[2025-07-22 21:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000088s ]
[2025-07-22 21:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-22 21:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-22 21:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000097s ]
[2025-07-22 21:10:03][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753189803  WHERE (  `id` = 750021 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-22 21:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000335s ]
[2025-07-22 21:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000260s ]
[2025-07-22 21:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000176s ]
[2025-07-22 21:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000110s ]
[2025-07-22 21:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000104s ]
[2025-07-22 21:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300437s ]
[2025-07-22 21:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000221s ]
[2025-07-22 21:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000188s ]
[2025-07-22 21:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000202s ]
[2025-07-22 21:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001452s ]
[2025-07-22 21:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-22 21:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000135s ]
[2025-07-22 21:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-22 21:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 21:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 21:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-22 21:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753189803  WHERE (  `id` = 750022 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000121s ]
[2025-07-22 21:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000368s ]
[2025-07-22 21:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000289s ]
[2025-07-22 21:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000174s ]
[2025-07-22 21:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000166s ]
[2025-07-22 21:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000163s ]
[2025-07-22 21:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300504s ]
[2025-07-22 21:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000273s ]
[2025-07-22 21:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000291s ]
[2025-07-22 21:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000128s ]
[2025-07-22 21:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001488s ]
[2025-07-22 21:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000194s ]
[2025-07-22 21:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000187s ]
[2025-07-22 21:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000092s ]
[2025-07-22 21:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-22 21:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000081s ]
[2025-07-22 21:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000113s ]
[2025-07-22 21:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753189803  WHERE (  `id` = 750023 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000116s ]
[2025-07-22 21:30:01][sql] CONNECT:[ UseTime:0.000443s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 21:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 20' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.008274s ]
[2025-07-22 21:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 20' [ RunTime:0.012752s ]
[2025-07-22 21:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004154s ]
[2025-07-22 21:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 20' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000864s ]
[2025-07-22 21:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 20' [ RunTime:0.002345s ]
[2025-07-22 22:10:01][sql] CONNECT:[ UseTime:0.000545s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000368s ]
[2025-07-22 22:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000423s ]
[2025-07-22 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000420s ]
[2025-07-22 22:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000280s ]
[2025-07-22 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000271s ]
[2025-07-22 22:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002542s ]
[2025-07-22 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000419s ]
[2025-07-22 22:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000303s ]
[2025-07-22 22:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000306s ]
[2025-07-22 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000224s ]
[2025-07-22 22:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299982s ]
[2025-07-22 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000261s ]
[2025-07-22 22:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000237s ]
[2025-07-22 22:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000156s ]
[2025-07-22 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000249s ]
[2025-07-22 22:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000152s ]
[2025-07-22 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000204s ]
[2025-07-22 22:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001441s ]
[2025-07-22 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000189s ]
[2025-07-22 22:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000139s ]
[2025-07-22 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000194s ]
[2025-07-22 22:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000174s ]
[2025-07-22 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000189s ]
[2025-07-22 22:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000122s ]
[2025-07-22 22:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000122s ]
[2025-07-22 22:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000184s ]
[2025-07-22 22:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-22 22:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000119s ]
[2025-07-22 22:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753193401  WHERE (  `id` = 750018 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000151s ]
[2025-07-22 22:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000164s ]
[2025-07-22 22:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000189s ]
[2025-07-22 22:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299190s ]
[2025-07-22 22:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000324s ]
[2025-07-22 22:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000247s ]
[2025-07-22 22:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000168s ]
[2025-07-22 22:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001490s ]
[2025-07-22 22:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000168s ]
[2025-07-22 22:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000196s ]
[2025-07-22 22:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000148s ]
[2025-07-22 22:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000149s ]
[2025-07-22 22:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000149s ]
[2025-07-22 22:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000201s ]
[2025-07-22 22:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753193402  WHERE (  `id` = 750019 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000167s ]
[2025-07-22 22:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000179s ]
[2025-07-22 22:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000134s ]
[2025-07-22 22:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299706s ]
[2025-07-22 22:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000136s ]
[2025-07-22 22:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000330s ]
[2025-07-22 22:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000336s ]
[2025-07-22 22:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001516s ]
[2025-07-22 22:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000168s ]
[2025-07-22 22:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000200s ]
[2025-07-22 22:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000151s ]
[2025-07-22 22:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-22 22:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 22:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000098s ]
[2025-07-22 22:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753193402  WHERE (  `id` = 750020 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000174s ]
[2025-07-22 22:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000344s ]
[2025-07-22 22:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000250s ]
[2025-07-22 22:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000270s ]
[2025-07-22 22:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000246s ]
[2025-07-22 22:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000215s ]
[2025-07-22 22:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000221s ]
[2025-07-22 22:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.301986s ]
[2025-07-22 22:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000224s ]
[2025-07-22 22:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000107s ]
[2025-07-22 22:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000123s ]
[2025-07-22 22:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001529s ]
[2025-07-22 22:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000114s ]
[2025-07-22 22:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000140s ]
[2025-07-22 22:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000090s ]
[2025-07-22 22:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000085s ]
[2025-07-22 22:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 22:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000108s ]
[2025-07-22 22:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753193402  WHERE (  `id` = 750021 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000123s ]
[2025-07-22 22:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000344s ]
[2025-07-22 22:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000256s ]
[2025-07-22 22:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000186s ]
[2025-07-22 22:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000111s ]
[2025-07-22 22:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000103s ]
[2025-07-22 22:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.299971s ]
[2025-07-22 22:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000265s ]
[2025-07-22 22:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000203s ]
[2025-07-22 22:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000328s ]
[2025-07-22 22:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001511s ]
[2025-07-22 22:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000172s ]
[2025-07-22 22:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000202s ]
[2025-07-22 22:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000093s ]
[2025-07-22 22:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-22 22:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 22:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-22 22:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753193403  WHERE (  `id` = 750022 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000176s ]
[2025-07-22 22:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000473s ]
[2025-07-22 22:10:03][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000248s ]
[2025-07-22 22:10:03][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000170s ]
[2025-07-22 22:10:03][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000165s ]
[2025-07-22 22:10:03][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000163s ]
[2025-07-22 22:10:03][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.304059s ]
[2025-07-22 22:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000315s ]
[2025-07-22 22:10:03][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000155s ]
[2025-07-22 22:10:03][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000121s ]
[2025-07-22 22:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001482s ]
[2025-07-22 22:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-22 22:10:03][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000135s ]
[2025-07-22 22:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-22 22:10:03][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 22:10:03][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-22 22:10:03][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000093s ]
[2025-07-22 22:10:03][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753193403  WHERE (  `id` = 750023 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000120s ]
[2025-07-22 22:30:01][sql] CONNECT:[ UseTime:0.000387s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 22:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 21' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.007851s ]
[2025-07-22 22:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 21' [ RunTime:0.011960s ]
[2025-07-22 22:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004458s ]
[2025-07-22 22:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 21' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000292s ]
[2025-07-22 22:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 21' [ RunTime:0.002054s ]
[2025-07-22 23:10:01][sql] CONNECT:[ UseTime:0.000425s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 23:10:01][sql] SHOW FULL COLUMNS FROM `ea_product_sold` [ RunTime:0.000471s ]
[2025-07-22 23:10:01][sql] SELECT `a`.`farmid` FROM `ea_product_sold` `a` INNER JOIN `ea_dev_secsn` `b` ON `a`.`sn`=`b`.`sn` WHERE (  `a`.`usertype` = 0 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000239s ]
[2025-07-22 23:10:01][sql] SHOW FULL COLUMNS FROM `ea_user_info` [ RunTime:0.000204s ]
[2025-07-22 23:10:01][sql] SELECT * FROM `ea_user_info` WHERE  `farmid` = '0216' OR `type` <= 1 [ RunTime:0.000119s ]
[2025-07-22 23:10:01][sql] SHOW FULL COLUMNS FROM `ea_house` [ RunTime:0.000248s ]
[2025-07-22 23:10:01][sql] SELECT * FROM `ea_house` WHERE (  `farmid` = '0216' ) AND `ea_house`.`delete_time` IS NULL [ RunTime:0.002515s ]
[2025-07-22 23:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_prod` [ RunTime:0.000161s ]
[2025-07-22 23:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000106s ]
[2025-07-22 23:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5924  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000132s ]
[2025-07-22 23:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_data` [ RunTime:0.000191s ]
[2025-07-22 23:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 897  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300813s ]
[2025-07-22 23:10:01][sql] SHOW FULL COLUMNS FROM `ea_batch` [ RunTime:0.000468s ]
[2025-07-22 23:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000336s ]
[2025-07-22 23:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 897 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000280s ]
[2025-07-22 23:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_in` [ RunTime:0.000261s ]
[2025-07-22 23:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5924  AND `batch_id` = 47 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000119s ]
[2025-07-22 23:10:01][sql] SHOW FULL COLUMNS FROM `ea_breed_dead` [ RunTime:0.000171s ]
[2025-07-22 23:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001443s ]
[2025-07-22 23:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_youth_detail` [ RunTime:0.000201s ]
[2025-07-22 23:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000112s ]
[2025-07-22 23:10:01][sql] SHOW FULL COLUMNS FROM `ea_sale_cullchick_detail` [ RunTime:0.000155s ]
[2025-07-22 23:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 897  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000139s ]
[2025-07-22 23:10:01][sql] SHOW FULL COLUMNS FROM `ea_house_adjust` [ RunTime:0.000148s ]
[2025-07-22 23:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000091s ]
[2025-07-22 23:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000084s ]
[2025-07-22 23:10:01][sql] SHOW FULL COLUMNS FROM `ea_cunlan_adjust` [ RunTime:0.000146s ]
[2025-07-22 23:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 897  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-22 23:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 47 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000093s ]
[2025-07-22 23:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753197001  WHERE (  `id` = 750018 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000116s ]
[2025-07-22 23:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000106s ]
[2025-07-22 23:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 5935  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000127s ]
[2025-07-22 23:10:01][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 872  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300261s ]
[2025-07-22 23:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000311s ]
[2025-07-22 23:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 872 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000302s ]
[2025-07-22 23:10:01][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 5935  AND `batch_id` = 24 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000309s ]
[2025-07-22 23:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001507s ]
[2025-07-22 23:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000160s ]
[2025-07-22 23:10:01][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 872  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000137s ]
[2025-07-22 23:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000089s ]
[2025-07-22 23:10:01][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-22 23:10:01][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 872  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000289s ]
[2025-07-22 23:10:01][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 24 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000215s ]
[2025-07-22 23:10:01][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753197001  WHERE (  `id` = 750019 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000183s ]
[2025-07-22 23:10:01][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000189s ]
[2025-07-22 23:10:01][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6033  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000257s ]
[2025-07-22 23:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1030  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300289s ]
[2025-07-22 23:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000239s ]
[2025-07-22 23:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1030 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000101s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6033  AND `batch_id` = 157 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001459s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000102s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1030  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000134s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000087s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1030  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-22 23:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 157 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000095s ]
[2025-07-22 23:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753197002  WHERE (  `id` = 750020 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-22 23:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000215s ]
[2025-07-22 23:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6865  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000278s ]
[2025-07-22 23:10:02][sql] SHOW FULL COLUMNS FROM `ea_hjxx_r2025` [ RunTime:0.000367s ]
[2025-07-22 23:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000198s ]
[2025-07-22 23:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000164s ]
[2025-07-22 23:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20240930070001' [ RunTime:0.000159s ]
[2025-07-22 23:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1332  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300511s ]
[2025-07-22 23:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000224s ]
[2025-07-22 23:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1332 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000096s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6865  AND `batch_id` = 460 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001445s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000105s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1332  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000136s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000082s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1332  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000080s ]
[2025-07-22 23:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 460 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000092s ]
[2025-07-22 23:10:02][sql] UPDATE `ea_breed_data`  SET `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753197002  WHERE (  `id` = 750021 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000108s ]
[2025-07-22 23:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000328s ]
[2025-07-22 23:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6871  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000130s ]
[2025-07-22 23:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000120s ]
[2025-07-22 23:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000108s ]
[2025-07-22 23:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080003' [ RunTime:0.000102s ]
[2025-07-22 23:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1724  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300777s ]
[2025-07-22 23:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000221s ]
[2025-07-22 23:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1724 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000174s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6871  AND `batch_id` = 844 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000197s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001433s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000107s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1724  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000133s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000206s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000272s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1724  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000231s ]
[2025-07-22 23:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 844 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000223s ]
[2025-07-22 23:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753197002  WHERE (  `id` = 750022 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000169s ]
[2025-07-22 23:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216' ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000294s ]
[2025-07-22 23:10:02][sql] SELECT * FROM `ea_product_sold` WHERE (  `houseid` = 6872  AND `status` = 1  AND `farmid` = '0216'  AND `usertype` = 0 ) AND `ea_product_sold`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000253s ]
[2025-07-22 23:10:02][sql] SELECT MAX(`watermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000186s ]
[2025-07-22 23:10:02][sql] SELECT MAX(`powermax`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000165s ]
[2025-07-22 23:10:02][sql] SELECT MAX(`feednum`) AS think_max FROM `ea_hjxx_r2025` WHERE  `recvtime` BETWEEN '2025-07-22 00:00:00' AND '2025-07-22 23:59:59'  AND `sn` = '20250118080006' [ RunTime:0.000160s ]
[2025-07-22 23:10:02][sql] SELECT * FROM `ea_breed_data` WHERE (  `farmid` = '0216'  AND `prod_id` = 1723  AND `prod_date` BETWEEN 1753113600 AND 1753199999 ) AND `ea_breed_data`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.300545s ]
[2025-07-22 23:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000356s ]
[2025-07-22 23:10:02][sql] SELECT * FROM `ea_house_prod` WHERE (  `id` = 1723 ) AND `ea_house_prod`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000331s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`inhouse_num`) AS think_sum FROM `ea_house_in` WHERE (  `houseid` = 6872  AND `batch_id` = 843 ) AND `ea_house_in`.`delete_time` IS NULL [ RunTime:0.000308s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_breed_dead` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_breed_dead`.`delete_time` IS NULL [ RunTime:0.001503s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_youth_detail` `a` INNER JOIN `ea_sale_youth` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000159s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`a`.`num`) AS think_sum FROM `ea_sale_cullchick_detail` `a` INNER JOIN `ea_sale_cullchick` `b` ON `a`.`sale_id`=`b`.`id` WHERE (  `a`.`prod_id` = 1723  AND `b`.`cdate` <= 1753199999 ) AND `a`.`delete_time` IS NULL [ RunTime:0.000138s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `source_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000091s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`adjust_num`) AS think_sum FROM `ea_house_adjust` WHERE (  `target_prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_house_adjust`.`delete_time` IS NULL [ RunTime:0.000083s ]
[2025-07-22 23:10:02][sql] SELECT SUM(`num`) AS think_sum FROM `ea_cunlan_adjust` WHERE (  `prod_id` = 1723  AND `cdate` <= 1753199999 ) AND `ea_cunlan_adjust`.`delete_time` IS NULL [ RunTime:0.000086s ]
[2025-07-22 23:10:02][sql] SELECT * FROM `ea_batch` WHERE (  `id` = 843 ) AND `ea_batch`.`delete_time` IS NULL LIMIT 1 [ RunTime:0.000123s ]
[2025-07-22 23:10:02][sql] UPDATE `ea_breed_data`  SET `cunlan` = 0 , `feedeggbi` = 0 , `waterfeedbi` = 0 , `egglu` = 0 , `update_time` = 1753197002  WHERE (  `id` = 750023 ) AND `ea_breed_data`.`delete_time` IS NULL [ RunTime:0.000110s ]
[2025-07-22 23:30:01][sql] CONNECT:[ UseTime:0.000325s ] mysql:host=127.0.0.1;port=3306;dbname=hkdb;charset=utf8
[2025-07-22 23:30:01][sql] INSERT INTO ea_hjxx_r2025(days,wd1,wd2,wd3,pjwd,sd,gzqd,kqzl,co2,yl,swwd,swsd,watermax,watermin,sn,recvtime)select any_value(dayage) as days,max(wd1) as wd1,max(wd2) as wd2,max(wd3) as wd3,round((avg(wd1)+avg(wd2)+avg(wd3))/3,1) as pjwd,max(sd) as sd, round(avg(gzqd),1) as gzqd, round(avg(kqzl),1) as kqzl,round(avg(co2),1) as co2, round(avg(yl),1) as yl, max(swwd) as swwd, max(swsd) as swsd, max(sbll) as watermax,min(sbll) as watermin, sn, date_format(recvtime,'%Y-%m-%d %H') as recvtime from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')= '2025-07-22 22' group by sn,date_format(recvtime,'%Y-%m-%d %H') [ RunTime:0.009078s ]
[2025-07-22 23:30:01][sql] delete from ea_hjxx_ls where date_format(recvtime,'%Y-%m-%d %H')<= '2025-07-22 22' [ RunTime:0.011877s ]
[2025-07-22 23:30:01][sql] drop temporary table if exists ea_newdev_temp [ RunTime:0.004134s ]
[2025-07-22 23:30:01][sql] insert into ea_hjxx_r2025(sn,days,recvtime,wd1,wd2,wd3,wd4,wd5,wd6,wd7,wd8,pjwd,sd,gzqd,co2,kqzl,yl,swwd,swsd,watermax,watermin,powermax,powermin,feednum) select sn,days,date_format(gettime,'%Y-%m-%d %H') as recvtime,max(round(case itemname when 'wd1' then itemval else 0 end,1)) wd1,max(round(case itemname when 'wd2' then itemval else 0 end,1)) wd2,max(round(case itemname when 'wd3' then itemval else 0 end,1)) wd3,max(round(case itemname when 'wd4' then itemval else 0 end,1)) wd4,max(round(case itemname when 'wd5' then itemval else 0 end,1)) wd5,max(round(case itemname when 'wd6' then itemval else 0 end,1)) wd6,max(round(case itemname when 'wd7' then itemval else 0 end,1)) wd7,max(round(case itemname when 'wd8' then itemval else 0 end,1)) wd8,if(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end)>0,round(sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then itemval else 0 end)/sum(case when itemname='wd1' or itemname='wd2' or itemname='wd3' or itemname='wd4' or itemname='wd5' or itemname='wd6' or itemname='wd7' or itemname='wd8' then 1 else 0 end),1),0) pjwd,max(round(case when itemname='sd1' OR itemname='sd2' then itemval else 0 end,1)) sd,if(sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END)>0,round(sum(case itemname when 'gzqd1' then itemval else 0 end)/sum(case itemname WHEN 'gzqd1' THEN 1 else 0 END),1),0) gzqd,max(case itemname when 'co21' then itemval else 0 end) co2,if(sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END)>0,round(sum(case itemname when 'kqzl1' then itemval else 0 end)/sum(case itemname WHEN 'kqzl1' THEN 1 else 0 END),1),0) kqzl,if(sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END)>0,round(sum(case itemname when 'fy1' then itemval else 0 end)/sum(case itemname WHEN 'fy1' THEN 1 ELSE 0 END),1),0) yl,max(round(case itemname when 'swwd' then itemval else 0 end,1)) swwd,max(case itemname when 'swsd' then itemval else 0 end) swsd,max(case itemname when 'sb1' then itemval else 0 end) watermax,min(case itemname when 'sb1' then itemval else 0 end) watermin,max(case itemname when 'dianbiao' then itemval else 0 end) powermax,min(case itemname when 'dianbiao' then itemval else 0 end) powermin,max(case itemname when 'feednum' then itemval else 0 end) feednum from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')= '2025-07-22 22' group by sn,days,date_format(gettime,'%Y-%m-%d %H') [ RunTime:0.000365s ]
[2025-07-22 23:30:01][sql] delete from ea_newdev_hours where date_format(gettime,'%Y-%m-%d %H')<= '2025-07-22 22' [ RunTime:0.002001s ]
