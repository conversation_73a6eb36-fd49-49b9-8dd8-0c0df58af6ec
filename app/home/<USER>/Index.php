<?php

namespace app\home\controller;

use app\home\model\HjxxLs;
use app\home\model\ArticleList;
use app\home\model\CunlanAdjust;
use app\home\model\DrugsBuy;
use app\home\model\DrugsList;
use app\home\model\DrugsReceive;
use app\home\model\EggStock;
use app\home\model\FeedendBuy;
use app\home\model\FeedendList;
use app\home\model\FeedendReceive;
use app\home\model\HjxxR2023;
use app\home\model\HjxxR2025;
use app\home\model\MsgList;
use app\home\model\Batch;
use app\home\model\BreedDead;
use app\home\model\DealerSale;
use app\home\model\DealerUser;
use app\home\model\HjxxDay;
use app\home\model\HjxxR2021;
use app\home\model\HjxxR2022;
use app\home\model\HjxxRealtime;
use app\home\model\HouseProd;
use app\home\model\NewdevHjxx;
use app\home\model\ProductList;
use app\home\model\ProductPlan;
use app\home\model\ProductPlanHouse;
use app\home\model\ProductSold;
use app\home\model\ReportDay;
use app\home\model\SellActivity;
use app\home\model\UserCustom;
use app\home\model\UserDealer;
use app\home\model\UserQuick;
use app\home\model\BreedData;
use app\home\model\House;
use app\home\model\UserAdmin;
use app\home\model\UserInfo;
use app\common\controller\AdminController;
use app\home\model\VaccineBuy;
use app\home\model\VaccineList;
use app\home\model\VaccineReceive;
use app\home\model\WareHouse;
use app\home\model\Warning;
use app\home\model\WarnVoiceList;
use app\home\model\YmList;
use app\home\model\YmprogramList;
use think\App;
use think\facade\Db;
use think\facade\Env;
use think\Model;


class Index1 extends AdminController
{
    public function __construct(App $app)
    {
        parent::__construct($app);

        $cdate = date('Y') . '年' . date('m') . '月' . date('d') . '日    ' . date('H') . ':' . date('i');
        $weekNum = ['日', '一', '二', '三', '四', '五', '六'];
        $cweek = '星期' . $weekNum[date('w')];
        $datetime = $cdate . '    ' . $cweek;
        $this->assign('datetime', $datetime);

    }

    /**
     * 后台主页
     * @return string
     * @throws \Exception
     */
    public function index()
    {
        return $this->fetch('', [
            'admin' => session('admin'),
        ]);
    }

    /**
     * 后台欢迎页
     * @return string
     * @throws \Exception
     */
    public function welcome()
    {

//        $farmid = session('admin.farmid');
//
//        $list = HouseProd::where('farmid', $farmid)->select();
//        $cunlan = 0;
//        foreach ($list as $key => $value) {
//            $cunlan += getCunlanProd($value->id);
//        };
//        $data['cunlan'] = $cunlan;
//
//        $data['powerNumSum'] = BreedData::where('farmid', $farmid)->sum('powernum');
//
//        $data['waterNumSum'] = BreedData::where('farmid', $farmid)->sum('waternum');
//
//        $data['feedNumSum'] = BreedData::where('farmid', $farmid)->sum('feednum');
//
//        $snList = ProductSold::where('farmid', $farmid)->where('usertype', 0)->where('status',1)->select();
//        $this->assign('snList', $snList);
//        $warnNum = 0;
//        $onlineNum = 0;
//        foreach ($snList as $key => $value) {
//            $warnNum += WarnVoiceList::where('sn', $value['sn'])->count();
//            $onlineDevList = getOnline($value['sn']);
//            $onlineNum += $onlineDevList['value'];
//        }
//        $data['warmNum'] = $warnNum;
//        $data['onlineNum'] = $onlineNum;
//
//        $this->assign('data', $data);
//
//
//        $quicks = UserQuick::field('id,title,icon,href')
//            ->where(['status' => 2])
//            ->where('farmid', $farmid)
//            ->order('hit', 'desc')
//            ->limit(6)
//            ->select();
//        $this->assign('quicks', $quicks);
//
//        // 获取公告
//        $articleList = ArticleList::order('create_time desc')->limit(6)->select();
//        $this->assign('articleList', $articleList);
//
//        //未读消息
//        $msgList = MsgList::where('farmid', $farmid)->where('isreader',0)->limit(6)->select();
//        $this->assign('msgList', $msgList);
//
//
//
//        return $this->fetch();

        //存栏数量

        //在线设备数量

        //报警条数

        //月度用料

        //月度用水

        //月度用电

        //快捷入口

        //系统公告

    }


    /**
     * 修改管理员信息
     * @return string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function editAdmin()
    {
        $id = session('admin.id');
        $row = (new UserAdmin())
            ->withoutField('password')
            ->find($id);
        empty($row) && $this->error('用户信息不存在');
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $this->isDemo && $this->error('演示环境下不允许修改');
            $rule = [];
            $this->validate($post, $rule);
            try {
                $save = $row
                    ->allowField(['head_img', 'phone', 'remark', 'update_time'])
                    ->save($post);
            } catch (\Exception $e) {
                $this->error('保存失败！');
            }
            $save ? $this->success('保存成功') : $this->error('保存失败');
        }
        $this->assign('row', $row);
        return $this->fetch();
    }

    /**
     * 修改密码
     * @return string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function editPassword()
    {
        $id = session('admin.id');
        $row = (new UserAdmin())
            ->withoutField('password')
            ->find($id);
        if (!$row) {
            $this->error('用户信息不存在');
        }
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $this->isDemo && $this->error('演示环境下不允许修改');
            $rule = [
                'password|登录密码' => 'require',
                'password_again|确认密码' => 'require',
            ];
            $this->validate($post, $rule);
            if ($post['password'] != $post['password_again']) {
                $this->error('两次密码输入不一致');
            }

            // 判断是否为演示站点
            $example = Env::get('easyadmin.example', 0);
            //$example == 1 && $this->error('演示站点不允许修改密码');

            try {
                $save = $row->save([
                    'password' => password($post['password']),
                ]);
            } catch (\Exception $e) {
                $this->error('保存失败');
            }
            if ($save) {
                $this->success('保存成功');
            } else {
                $this->error('保存失败');
            }
        }
        $this->assign('row', $row);
        return $this->fetch();
    }

    /**
     * 客户当日温度图表
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function bigview_info_echart()
    {
        if ($this->request->isAjax()) {
            //$sn = ProductSold::where('farmid', session('admin.farmid'))->where('usertype', 0)->limit(1)->value('sn');
            $sn = HjxxRealtime::order('recvtime desc')->limit(1)->value('sn');
            //当日温度曲线
            $dataList = HjxxLs::where('sn', $sn)->whereDay('recvtime')->order('recvtime desc')->limit(20)->select();

            $xdata = [];
            $wd1 = [];
            $wd2 = [];
            $wd3 = [];
            foreach ($dataList as $key => $value) {
                $xdata[] = date('H:i:s', strtotime($value['recvtime']));
                $wd1[] = $value['wd1'];
                $wd2[] = $value['wd2'];
                $wd3[] = $value['wd3'];
            }
            $data['xdata'] = $xdata;
            $data['wd1'] = $wd1;
            $data['wd2'] = $wd2;
            $data['wd3'] = $wd3;
            $data['sn'] = $sn;

            return json($data);
        }
    }
}
