<link rel="stylesheet" href="__STATIC__/home/<USER>/welcome.css?v={:time()}" media="all">

<div class="layuimini-container">
    <div class="layuimini-main">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md8">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md7">
                        <div class="layui-card">
                            <div class="layui-card-header">
                                <i class="fa fa-warning icon"></i>数据统计
                            </div>
                            <div class="layui-card-body">
                                <div class="welcome-module">
                                    <div class="layui-row layui-col-space10">
                                        { php }
                                        $stats = [
                                        ['title'=> '存栏数量', 'value'=> $data['cunlan'] ?? 0, 'unit'=> '当前存栏数量(只)', 'color'=> 'layui-bg-orange'],
                                        ['title'=> '在线设备数量', 'value'=> $data['onlineNum'] ?? 0, 'unit'=> '数量', 'color'=> 'layui-bg-blue'],
                                        ['title'=> '报警条数', 'value'=> $data['warnNum'] ?? 0, 'unit'=> '条数', 'color'=> 'layui-bg-red'],
                                        ['title'=> '月度用料', 'value'=> $data['feedNumSum'] ?? 0, 'unit'=> '当月耗料量(Kg)', 'color'=> 'layui-bg-cyan'],
                                        ['title'=> '月度用水', 'value'=> $data['waternum'] ?? 0, 'unit'=> '当月用水量(吨)', 'color'=> 'layui-bg-green'],
                                        ['title'=> '月度用电', 'value'=> $data['powernum'] ?? 0, 'unit'=> '当月用电量(度)', 'color'=> 'layui-bg-red'],
                                        ];
                                        {/php }
                                        { foreach $stats as $item }
                                        <div class="layui-col-xs4">
                                            <div class="panel layui-bg-number">
                                                <div class="panel-body">
                                                    <div class="panel-title">
                                                        <span class="label pull-right {$item.color}">实时</span>
                                                        <h5>{$item.title}</h5>
                                                    </div>
                                                    <div class="panel-content">
                                                        <h1 class="no-margins">{$item.value}</h1>
                                                        <small>{$item.unit}</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        { /foreach }
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="layui-col-md5">
                        <div class="layui-card">
                            <div class="layui-card-header">
                                <i class="fa fa-credit-card icon icon-blue"></i>快捷入口
                            </div>
                            <div class="layui-card-body">
                                <div class="welcome-module">
                                    <div class="layui-row layui-col-space10 layuimini-qiuck">
                                        { notempty name="quicks" }
                                        { foreach $quicks as $vo }
                                        <div class="layui-col-xs4 layuimini-qiuck-module">
                                            <a layuimini-content-href="{:url($vo.href)}" data-title="{$vo.title}">
                                                <i class="{$vo.icon|raw}"></i><cite>{$vo.title}</cite>
                                            </a>
                                        </div>
                                        { /foreach }
                                        { /notempty }
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="layui-col-md12">
                        <div class="layui-card">
                            <div class="layui-card-header">
                                <i class="fa fa-line-chart icon"></i>今天温度
                            </div>
                            <div class="layui-card-body">
                                <div id="echarts-records" style="width: 100%;min-height:500px"></div>
                            </div>
                            <div id="wdval" class="layui-hide"></div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="layui-col-md4">
                { php }
                $cards = [
                    ['title'=> '系统公告', 'icon'=> 'fa-bullhorn', 'list'=> $articleList, 'moreUrl'=> '/home/<USER>/index?id=1', 'moreTitle'=> '公告信息', 'idKey'=> 'notice', 'extraKey'=> 'author'],
                    ['title'=> '未读消息', 'icon'=> 'fa-commenting-o', 'list'=> $msgList, 'moreUrl'=> '/home/<USER>/index.html', 'moreTitle'=> '系统消息', 'idKey'=> 'msg', 'extraKey'=> 'id'],
                ];
                {/php }
                { foreach $cards as $card }
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="layui-row">
                            <div class="layui-col-sm6"><i class="fa {$card.icon} icon icon-tip"></i>{$card.title}</div>
                            <div class="layui-col-sm6" style="text-align: right">
                                <a target="_self" layuimini-content-href="{$card.moreUrl}" data-title="{$card.moreTitle}" href="javascript:void(0)">
                                    <i class="fa fa-ellipsis-h icon icon-tip"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body">
                        { volist $card.list as $vo }
                        <div class="layuimini-notice" id="{$card.idKey}">
                            <div class="layuimini-notice-title">{$vo.title}</div>
                            <div class="layuimini-notice-extra">{$vo.create_time}</div>
                            <div class="layuimini-notice-content layui-hide">{$vo.content}</div>
                            <div class="layui-hide" id="{$card.extraKey}">{$vo[$card.extraKey]}</div>
                        </div>
                        { /volist }
                    </div>
                </div>
                { /foreach }

            </div>
        </div>
    </div>
</div>